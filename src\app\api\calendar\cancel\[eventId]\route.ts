import { NextRequest, NextResponse } from 'next/server';
import { ConfidentialClientApplication } from '@azure/msal-node';
import axios from 'axios';
import type { NextApiRequest } from 'next';

const config = {
  auth: {
    clientId: process.env.CLIENT_ID!,
    authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
    clientSecret: process.env.CLIENT_SECRET!,
  },
};

const cca = new ConfidentialClientApplication(config);

// Correct type for Route Handler Context
export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ eventId: string }> }
) {
  const { eventId } = await context.params;

  try {
    const result = await cca.acquireTokenByClientCredential({
      scopes: ['https://graph.microsoft.com/.default'],
    });

    const accessToken = result?.accessToken;

    await axios.delete(
      `https://graph.microsoft.com/v1.0/users/${process.env.SENDER_EMAIL}/events/${eventId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    return NextResponse.json({ message: 'Interview cancelled successfully' });
  } catch (error: any) {
    console.error('Error cancelling interview:', error.response?.data || error.message);
    return NextResponse.json({ error: 'Failed to cancel interview' }, { status: 500 });
  }
}
