# AI RecruitPro - Component Library

A comprehensive React component library built with Next.js, TypeScript, and Tailwind CSS for recruitment and HR applications.

## 🚀 Features

- **Modern Tech Stack**: Built with Next.js 15, React 19, TypeScript, and Tailwind CSS v4
- **Modular Architecture**: Well-organized component structure with clear separation of concerns
- **TypeScript Support**: Fully typed components with comprehensive interfaces
- **Responsive Design**: Mobile-first approach with responsive layouts
- **Accessibility**: Components built with accessibility best practices
- **Customizable**: Easy to theme and customize with Tailwind CSS

## 🚀 Getting Started

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Run Development Server**
   ```bash
   npm run dev
   ```

3. **View Dashboard Example**
   Navigate to `/dashboard` to see all components in action

## 📁 Component Categories

### UI Components
- **Button**: Versatile button component with multiple variants and sizes
- **Card**: Flexible card container with header, body, and footer sections
- **Badge**: Status and category indicators
- **Input**: Form input with validation and error states
- **Icon**: SVG icon library with recruitment-specific icons

### Layout Components
- **Layout**: Main application layout with sidebar and header
- **Sidebar**: Navigation sidebar with user profile section
- **Header**: Application header with search and user menu

### Dashboard Components
- **MetricCard**: Display key metrics with trend indicators
- **JobPostingCard**: Job listing cards with actions
- **CandidateCard**: Candidate profile cards with contact options
- **QuickAction**: Action cards for common tasks

### Form Components
- **SearchBar**: Advanced search with filters
- **FilterDropdown**: Multi-select filter dropdown
- **DataTable**: Sortable data table with custom renderers
- **FormField**: Unified form field component

## 📖 Usage Examples

### Basic Button Usage
```tsx
import { Button } from '@/components';

<Button variant="primary" size="md" onClick={handleClick}>
  Click Me
</Button>
```

### Layout with Sidebar
```tsx
import { Layout } from '@/components';

<Layout title="Dashboard" showSearch={true}>
  <div>Your content here</div>
</Layout>
```

### Metric Cards
```tsx
import { MetricCard } from '@/components';

const metric = {
  id: '1',
  title: 'Active Jobs',
  value: '25',
  change: 12,
  changeType: 'increase',
  icon: 'jobs',
  color: 'blue'
};

<MetricCard metric={metric} />
```
