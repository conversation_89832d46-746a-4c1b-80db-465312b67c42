// app/api/interview/cancel/[eventId]/route.js
import cca from '@/lib/msalClient';
import axios from 'axios';

export async function DELETE(
  req: Request, 
  { params }: { params: Promise<{ eventId: string }> }
) {
  const { eventId } = await params;

  try {
    const tokenResponse = await cca.acquireTokenByClientCredential({
      scopes: ['https://graph.microsoft.com/.default'],
    });

    if (!tokenResponse || !tokenResponse.accessToken) {
      throw new Error('Failed to acquire access token');
    }
    const accessToken = tokenResponse.accessToken;

    await axios.delete(
      `https://graph.microsoft.com/v1.0/users/${process.env.SENDER_EMAIL}/events/${eventId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    return new Response(JSON.stringify({ message: 'Interview cancelled successfully' }), {
      status: 200,
    });
  } catch (error: any) {
    console.error('Error cancelling interview:', error.response?.data || error.message);
    return new Response(JSON.stringify({ error: 'Failed to cancel interview' }), {
      status: 500,
    });
  }
}
