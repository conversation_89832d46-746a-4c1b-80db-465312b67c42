import axios from "axios";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

// Schema Definitions
const employmentHistorySchema = z.object({
  role: z.string(),
  organization: z.string(),
  start_date: z.string(),
  end_date: z.string(),
});

const educationSchema = z.object({
  degree: z.string(),
  start_date: z.string(),
  end_date: z.string(),
  field_of_study: z.string(),
  institution: z.string().optional(),
});

const resumeSchema = z.object({
  name: z.string(),
  email: z.string().email(),
  phone_number: z.string(),
  years_of_experience: z.string(),
  experience_summary: z.string(),
  achievements: z.array(z.string()),
  certifications: z.array(z.string()),
  education: z.array(educationSchema),
  employment_history: z.array(employmentHistorySchema),
  key_skills: z.array(z.string()),
  Resume_Explanation: z.string().optional(),
  Resume_Percentage: z.string().optional(),
  Job_id: z.string().uuid(),
  current_location: z.string(),
  flexible_with_relocation: z.string(),
  current_notice_period: z.string(),
  current_ctc: z.string(),
  expected_ctc: z.string(),
  reason_for_change: z.string().optional(),
  fileid: z.string(),
  candidateid: z.string().optional(),
  status: z.string().optional(),
  reference_mode: z.string().optional(),
  reference_person: z.string().optional(),
  Priority: z.union([z.number(), z.null()]).optional(),
});

export type ResumeSchema = z.infer<typeof resumeSchema>;





// post 

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

     

    const { candidate_id, status, level, is_ai_interview, panel, start_time, end_time, decision, interviewnotes, video, summary, timestamp } = body;
    console.log("Request Body:");

  
 const { data: responseData } = await axios.post( // axios.post(
      `${process.env.NODE_RED_URL}/scheduleInterviewWithAI`,
      { status, candidate_id, level, is_ai_interview, panel, start_time, end_time, decision, interviewnotes, video, summary, timestamp }
    );

    return NextResponse.json(responseData, { status: 200 });

  
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation failed", errors: error.issues },
        { status: 400 },
      );
    }
    return Response.json({ error });
  }
}

// ---------- PUT ----------
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();

   
    const { candidateid, status } = body;

    if (!candidateid || !status) {
      return NextResponse.json(
        { message: "Missing candidateid or status" },
        { status: 400 }
      );
    }

    const { data: responseData } = await axios.put(
      `${process.env.NODE_RED_URL}/candidate-scheduleai-interview`,
      { status, candidateid }
    );

    return NextResponse.json(responseData, { status: 200 });
  } catch (error) {
    console.error("PUT request error:", error);
    return NextResponse.json(
      { 
        message: "Internal server error", 
        error: error instanceof Error ? error.message : "Unknown error" 
      },
      { status: 500 }
    );
  }
}

// ---------- Helpers ----------
// function handleAxiosError(error: unknown) {
//   if (isAxiosError(error)) {
//     console.error("Axios Error:", error.message);
//     return NextResponse.json(
//       {
//         message: "External API error",
//         error: error.response?.data || error.message,
//       },
//       { status: error.response?.status || 500 }
//     );
//   }

//   console.error("Unexpected Error:", error);
//   return NextResponse.json(
//     { message: "Unexpected server error", error },
//     { status: 500 }
//   );
// }

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const candidateid = searchParams.get("candidate_id");

    let record;

    if(candidateid ) {
        const response = await axios.get(
        `${process.env.NODE_RED_URL}/getAIInterviewList?candidate_id=${candidateid}`);
      record = response.data;
      console.log("record-----", record);
        return new NextResponse(
        JSON.stringify(record),
        {
          headers: { "Content-Type": "application/json" },
          status: 200,
        },
      );
    }
 
  } catch (error) {
    // Handle errors
    return new NextResponse(JSON.stringify({ error: (error as any).message || 'Unknown error' }), {
      headers: { "Content-Type": "application/json" },
      status: 500,
    });
  }
}
