import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(req: NextRequest) {
  try {
    const {
      candidateId,
      firstName,
      email,
      jobTitleId,
      originalDate,
      originalTimeSlot,
      reason,
      scheduleformLink,
    } = await req.json();

    const jobTitleResponse = await fetch(`${process.env.NODE_RED_URL}/getjob-descid?jobdescid=${jobTitleId}`);
    const jobTitle = (await jobTitleResponse.json()).jobtitle;

    const companyName = req.headers.get("X-Auth-User-Organization");

    // Get access token for Microsoft Graph API
    const tokenResponse = await axios.post(
      `https://login.microsoftonline.com/${process.env.TENANT_ID}/oauth2/v2.0/token`,
      new URLSearchParams({
        client_id: process.env.CLIENT_ID!,
        client_secret: process.env.CLIENT_SECRET!,
        scope: 'https://graph.microsoft.com/.default',
        grant_type: 'client_credentials',
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    const accessToken = (tokenResponse.data as { access_token: string }).access_token;

  const emailBody = `
  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2>Interview Reschedule Notification</h2>

    <p>Dear ${firstName},</p>

    <p>
      We hope this message finds you well. We would like to inform you that your scheduled AI interview for the position of <strong>${jobTitle}</strong> at <strong>${companyName}</strong> needs to be rescheduled.
    </p>

    <p>
      <strong>Original Schedule:</strong><br>
      Date: ${originalDate}<br>
      Time: ${originalTimeSlot}
    </p>

    <p>
      <strong>Reason for Rescheduling:</strong><br>
      ${reason || "Not specified."}
    </p>

    <p>
      To proceed, please choose a new date and time for your interview using the link below:
    </p>

    <p>
      <a href="${scheduleformLink}" style="text-decoration: underline;">
        Click here to reschedule your interview
      </a>
    </p>

    <p>
      Kindly complete the rescheduling within <strong>48 hours</strong> to avoid any delays in your application process.
    </p>

    <p>
      We apologize for any inconvenience this may cause and appreciate your understanding and flexibility.
    </p>

    <p>
      Best regards,<br>
      <strong>${companyName} Recruitment Team</strong>
    </p>
  </div>
`;



    await axios.post(
      `https://graph.microsoft.com/v1.0/users/${process.env.SENDER_EMAIL}/sendMail`,
      {
        message: {
          subject: `Interview Reschedule Request - ${jobTitle} at ${companyName}`,
          body: {
            contentType: 'HTML',
            content: emailBody,
          },
          toRecipients: [
            {
              emailAddress: {
                address: email,
              },
            },
          ],
        },
        saveToSentItems: 'true',
      },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return NextResponse.json({
      success: true,
      message: 'Reschedule email sent successfully',
    });
  } catch (error) {
    console.error('Error sending reschedule email:', error);
    return NextResponse.json(
      { error: 'Failed to send reschedule email' },
      { status: 500 }
    );
  }
}
