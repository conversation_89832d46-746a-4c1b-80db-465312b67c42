import { NextRequest, NextResponse } from "next/server";
import axios from "axios";
import { ConfidentialClientApplication } from "@azure/msal-node";

const msalConfig = {
  auth: {
    clientId: process.env.CLIENT_ID!,
    authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
    clientSecret: process.env.CLIENT_SECRET!,
  },
};
const cca = new ConfidentialClientApplication(msalConfig);

interface InterviewNotificationData {
  candidate_id: string;
  candidate_name: string;
  candidate_email: string;
  interviewer_id: string;
  interviewer_name: string;
  interviewer_email: string;
  level: string;
  scheduled_date: string; // YYYY-MM-DD
  scheduled_time: string; // HH:mm (24 hr)
  timezone: string;       // e.g. "Asia/Kolkata"
  job_title: string;
  job_description?: string;
  resume_url?: string;
  notes?: string;
  // Optional: You may include existing meeting info but ignored here
  meeting_link?: string;
  meeting_id?: string;
  meeting_passcode?: string;
  meeting_join_url?: string;
}

export async function POST(req: NextRequest) {
  try {
    const body: InterviewNotificationData = await req.json();

    // Basic required fields validation
    const requiredFields = [
      "candidate_id",
      "candidate_name",
      "interviewer_name",
      "interviewer_email",
      "level",
      "scheduled_date",
      "scheduled_time",
      "job_title",
    ];

    for (const field of requiredFields) {
      if (!body[field as keyof InterviewNotificationData]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    const {
      candidate_id: _candidate_id,
      candidate_name,
      candidate_email,
      interviewer_name: _interviewer_name,
      interviewer_email,
      level,
      scheduled_date,
      scheduled_time,
      timezone: _timezone,
      job_title,
      job_description: _job_description,
      resume_url: _resume_url,
      notes: _notes,
    } = body;

    // Acquire Azure AD access token for Microsoft Graph
    const tokenResponse = await cca.acquireTokenByClientCredential({
      scopes: ["https://graph.microsoft.com/.default"],
    });

    if (!tokenResponse || !tokenResponse.accessToken) {
      return NextResponse.json(
        { error: "Failed to acquire Microsoft Graph token" },
        { status: 500 }
      );
    }
    const accessToken = tokenResponse.accessToken;

    // Organizer must be a real mailbox-enabled Teams user from your tenant
    const organizerEmail = process.env.SENDER_EMAIL || interviewer_email;

    // Compute ISO strings for meeting start/end (assume 1 hour duration)
    const startDateTimeISO = new Date(
      `${scheduled_date}T${scheduled_time}:00`
    ).toISOString();
    const endDateTimeISO = new Date(
      new Date(startDateTimeISO).getTime() + 60 * 60 * 1000
    ).toISOString();

    // Prepare attendees array (presenter is interviewer, candidate is attendee if email provided)
    const attendees = [
      { upn: interviewer_email, role: "presenter" },
      ...(candidate_email ? [{ upn: candidate_email, role: "attendee" }] : []),
    ];

    // Create the Teams online meeting
    let teamsMeetingData;
    try {
      const graphResponse = await axios.post(
        `https://graph.microsoft.com/v1.0/users/${organizerEmail}/onlineMeetings`,
        {
          subject: `${level} - ${candidate_name} for ${job_title}`,
          startDateTime: startDateTimeISO,
          endDateTime: endDateTimeISO,
          participants: { attendees },
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );
      teamsMeetingData = graphResponse.data;
    } catch (err: any) {
      const errorDetails = err?.response?.data || err.message || err;
      console.error("❌ Teams Graph API error:", errorDetails);
      return NextResponse.json(
        {
          error: "Failed to create Microsoft Teams meeting",
          details: errorDetails,
        },
        { status: 500 }
      );
    }

    // Extract needed info from meeting creation response
    const teamsJoinUrl = (teamsMeetingData && typeof teamsMeetingData === 'object' && 'joinWebUrl' in teamsMeetingData)
      ? (teamsMeetingData as { joinWebUrl: string }).joinWebUrl || ""
      : "";
    const teamsMeetingId = (teamsMeetingData && typeof teamsMeetingData === 'object' && 'id' in teamsMeetingData)
      ? (teamsMeetingData as { id: string }).id || ""
      : "";

    // TODO: Your email sending logic here using teamsJoinUrl...
    // For example, send emails to candidate and interviewer with join links etc.

    // Respond with meeting info so frontend or other services can use it
    return NextResponse.json({
      success: true,
      message: "Microsoft Teams meeting created successfully",
      teamsJoinUrl,
      teamsMeetingId,
    });
  } catch (error: any) {
    console.error("Interview notification error:", error);
    return NextResponse.json(
      { error: "Internal Server Error", details: error.message || error },
      { status: 500 }
    );
  }
}
