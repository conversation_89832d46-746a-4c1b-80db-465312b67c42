// create get method for fetching profile data from auth0
import { NextRequest, NextResponse, NextFetchEvent } from "next/server";
import axios from "axios";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const res = NextResponse.next();
    const requestHeaders = new Headers(request.headers);
    const user_id = requestHeaders.get("X-Auth-User-ID")!;
    const email = requestHeaders.get("X-Auth-User-Email")!;


    if (email) {
      // Fetch a specific record from the external API
       const ress = await fetch(
        `${process.env.NODE_RED_URL}/Get-Employee-By-Id1?email=${email}`
      );
      const record = await ress.json();  // Parse the response as JSON

      if (!record) {
        return new NextResponse(JSON.stringify({ error: "Record not found" }), {
          headers: { "Content-Type": "application/json" },
          status: 404,
        });
      }

      return new NextResponse(JSON.stringify(record.Items[0]), {
        headers: { "Content-Type": "application/json" },
      });
    }
  } catch (error) {
    console.error("Error fetching profile data:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to fetch profile data" }),
      { status: 500 },
    );
  }
}