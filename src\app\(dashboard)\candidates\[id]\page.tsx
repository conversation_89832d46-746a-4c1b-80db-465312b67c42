"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { <PERSON><PERSON>, <PERSON>, Badge, Icon, FormField } from "@/components";
import { cn, formatDateConsistent } from "@/lib/utils";
import { useToast } from "@/components/ui/Toast";
import DateTimePicker from "@/components/interview/DateTimePicker";
import InterviewerSelector from "@/components/interview/InterviewerSelector";
import { InterviewSchedule, SchedulingFormData } from "@/types";

// Interface for candidate data from API
interface ApiCandidateData {
  scheduled_date: any;
  form_responded: any;
  candidateid: string;
  form_responded_at: any;
  name: string;
  email: string;
  phone_number?: string;
  years_of_experience: string;
  experience_summary: string;
  achievements: string[];
  certifications: string[];
  education: Array<{
    degree: string;
    start_date: string;
    end_date: string;
    field_of_study: string;
    institution?: string;
  }>;
  employment_history: Array<{
    role: string;
    organization: string;
    start_date: string;
    end_date: string;
  }>;
  key_skills: string[];
  current_location: string;
  flexible_with_relocation: string;
  current_notice_period: string;
  current_ctc: string;
  expected_ctc: string;
  reason_for_change?: string;
  fileid: string;
  status: string;
  created_on: string;
  Job_id?: string;
  interview_eval_details?: any;
  interview_time_slot?: string;
  interview_date?: string;
}

// Interview level definitions
const INTERVIEW_LEVELS = [
  {
    id: 1,
    name: "AI Interview",
    description: "Automated AI screening",
    type: "automated",
  },
  {
    id: 2,
    name: "Technical Interview",
    description: "Technical assessment with interviewer",
    type: "manual",
  },
  {
    id: 3,
    name: "Manager Round",
    description: "Management interview",
    type: "manual",
  },
  {
    id: 4,
    name: "HR Round",
    description: "HR final interview",
    type: "manual",
  },
];

export default function CandidateDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const candidateId = params.id as string;

  const [candidate, setCandidate] = useState<ApiCandidateData | null>(null);
  const [candidateAiSummary, setCandidateAiSummary] = useState();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentLevel, setCurrentLevel] = useState<number>(1);
  const [selectedInterviewer, setSelectedInterviewer] = useState<string>("");
  const [selectedInterviewerName, setSelectedInterviewerName] =
    useState<string>("");
  const [interviewers, setInterviewers] = useState<Array<{ name: string }>>([]);
  const [interviewersLoading, setInterviewersLoading] = useState(false);
  const [levelUpdateLoading, setLevelUpdateLoading] = useState(false);
  const [showResumePreview, setShowResumePreview] = useState(false);

  // Enhanced scheduling state
  const [schedulingMode, setSchedulingMode] = useState<"simple" | "advanced">("simple");
  const [scheduledInterviews, setScheduledInterviews] = useState<InterviewSchedule[]>([]);
  const [schedulingData, setSchedulingData] = useState<SchedulingFormData>({
    interviewer_id: "",
    interviewer_name: "",
    scheduled_date: new Date(),
    scheduled_time: "",
    timezone: "Asia/Kolkata",
    notes: "",
  });
  const [schedulingLoading, setSchedulingLoading] = useState(false);
  const [schedulingErrors, setSchedulingErrors] = useState<{ [key: string]: string }>({});
  const [showSchedulingForm, setShowSchedulingForm] = useState(false);
  const [currentSchedulingLevel, setCurrentSchedulingLevel] = useState<number | null>(null);
  const [statusSyncLoading, setStatusSyncLoading] = useState(false);

  // Level 1 AI Interview specific state
  const [confirmingInterview, setConfirmingInterview] = useState(false);
  const [reschedulingInterview, setReschedulingInterview] = useState(false);
  const [showRescheduleForm, setShowRescheduleForm] = useState(false);
  const [interviewLinkSent, setInterviewLinkSent] = useState(false);
  const [rescheduleData, setRescheduleData] = useState({
    reason: "",
  });

  // Add these state variables
  const [aiInterviewDecision, setAiInterviewDecision] = useState<string>("");
  const [aiInterviewSummary, setAiInterviewSummary] = useState<string>("");

  // Add these state variables for interview completion
  const [completingInterview, setCompletingInterview] = useState<number | null>(null);
  const [submittingInterviewDecision, setSubmittingInterviewDecision] = useState<boolean>(false);

  const [aiIterviewDetails, setAiInterviewDetails] = useState<any>(null);
  const [technicalInterviewDetails, setTechnicalInterviewDetails] =
    useState<any>(null);

  // Add state for Level 2 interview workflow
  const [technicalInterviewData, setTechnicalInterviewData] = useState<any>(null);
  const [loadingTechnicalInterview, setLoadingTechnicalInterview] = useState(false);
  const [showDecisionForm, setShowDecisionForm] = useState<number | null>(null);
  const [interviewDecision, setInterviewDecision] = useState("");
  const [interviewSummary, setInterviewSummary] = useState("");
  const [submittingDecision, setSubmittingDecision] = useState(false);
  const [cancelingInterview, setCancelingInterview] = useState(false);
  const [technicalInterviewScheduled, setTechnicalInterviewScheduled] = useState<any>(null);

  // Add this handler function
  const handleAiInterviewDecision = async () => {
    if (!aiInterviewDecision || !aiInterviewSummary.trim()) {
      showToast("Please select a decision and provide a summary", "error");
      return;
    }

    setSubmittingDecision(true);
    try {
      // Update the interview decision and summary
      const response = await fetch("/api/interview", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          candidateid: candidateId,
          decision: aiInterviewDecision,
          summary: aiInterviewSummary,
          timestamp: aiIterviewDetails.timestamp,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update interview decision");
      }

      // If passed, move to next level
      if (aiInterviewDecision === "pass") {
        setCurrentLevel(2);
        showToast(
          "Candidate passed! Moving to Level 2 (Technical Interview)",
          "success"
        );
      } else {
        // Update candidate status to rejected
        await fetch("/api/candidate", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            candidateid: candidateId,
            status: "rejected",
          }),
        });
        showToast(
          "Candidate has been rejected based on AI interview",
          "warning"
        );
      }

      // Refresh candidate data
      await fetchCandidateDetails();

      // Reset form
      setAiInterviewDecision("");
      setAiInterviewSummary("");
    } catch (error) {
      console.error("Decision submission error:", error);
      showToast("Failed to submit decision. Please try again.", "error");
    } finally {
      setSubmittingDecision(false);
    }
  };

  const { showToast } = useToast();

  // Fetch candidate details
  const fetchCandidateDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/candidate?candidateid=${candidateId}`);
      const responseLevel = await fetch(
        `/api/interview?candidateId=${candidateId}`
      );
      const responseAisummary = await fetch(
        `/api/ai-scheduling?candidate_id=${candidateId}`
      );

      const interviewerData = await fetch(
        `/api/employee/get-designation`
      );

      const technicalinterviewData = await fetch(
        `/api/interview/technicalinterview?candidate_id=${candidateId}&roundName=Technical`
      );

      const technicalinterviewDetails = await technicalinterviewData.json();
      
      setTechnicalInterviewScheduled(technicalinterviewDetails);

      console.log(technicalinterviewDetails, "technicalinterviewDetails>>>>");

     
      

      if (!interviewerData.ok) {
        throw new Error("Failed to fetch interviewer details");
      }

      const interviewerDetails = await interviewerData.json();

      console.log(interviewerDetails, "interviewerDetails>>>>");
      // ... rest of the code remains the same ...
    setTechnicalInterviewDetails(interviewerDetails);    




      const aisummaryData = await responseAisummary.json();
      console.log(aisummaryData, "responseAisummary>>>>");

      if (!response.ok) {
        throw new Error("Failed to fetch candidate details");
      }

      const data = await response.json();
      const leveldata = await responseLevel.json();
      setCandidate(data);

      setCandidateAiSummary(aisummaryData);

      // Determine current interview level based on status or interview history

      console.log(leveldata[0], "<<<<<<< responseLevel Data >>>>>>");

      const level = determineCurrentLevel(leveldata);

      console.log(level, "<<<<<<< Current Level >>>>>>");
      setAiInterviewDetails(leveldata[0]);

      console.log(level, "<<<<<<< Current Level >>>>>>");
      setCurrentLevel(level);

      // Reset interviewer selection when level changes
      setSelectedInterviewer("");
    } catch (err) {
      console.error("Error fetching candidate details:", err);
      setError(
        err instanceof Error ? err.message : "Failed to fetch candidate details"
      );
    } finally {
      setLoading(false);
    }
  };

  console.log(candidate, "<<<<<<< Candidate Data >>>>>>");

  console.log(technicalInterviewDetails, "<<<<<<< technicalInterviewDetails >>>>>>");
   console.log(technicalInterviewScheduled, "technicalInterviewScheduled>>>>");

  // Fetch interviewers for Level 2
  const fetchInterviewers = async () => {
    try {
      setInterviewersLoading(true);
      const response = await fetch("/api/auth/user-by-role");
      if (response.ok) {
        const data = await response.json();
        setInterviewers(data.data || []);
      } else {
        console.error("Failed to fetch interviewers");
      }
    } catch (err) {
      console.error("Error fetching interviewers:", err);
    } finally {
      setInterviewersLoading(false);
    }
  };

  // Determine current interview level based on candidate data
  const determineCurrentLevel = (candidateData: any[]): number => {
    if (!Array.isArray(candidateData)) return 1;

    const levelStatusMap = new Map<
      number,
      { status: string; decision?: string }
    >();

    candidateData.forEach((interview) => {
      const levelNum = parseInt(interview.level?.replace("level", "") || "0");
      if (levelNum > 0) {
        levelStatusMap.set(levelNum, {
          status: interview.status,
          decision: interview.decision,
        });
      }
    });

    // ✅ Filter only those levels that are Completed **AND** Passed
    const completedLevels = [...levelStatusMap.entries()]
      .filter(
        ([_, data]) => data.status === "Completed" && data.decision === "pass"
      )
      .map(([level]) => level);

    const maxCompleted =
      completedLevels.length > 0 ? Math.max(...completedLevels) : 0;

    // If a scheduled interview exists, and it's for levelX, then previous level is completed
    const scheduledLevels = [...levelStatusMap.entries()]
      .filter(([_, data]) => data.status === "Scheduled")
      .map(([level]) => level);

    if (scheduledLevels.length > 0) {
      const minScheduled = Math.min(...scheduledLevels);
      return minScheduled; // Next interview to be attempted
    }

    return maxCompleted + 1 || 1;
  };

  // Fetch scheduled interviews for the candidate
  const fetchScheduledInterviews = async () => {
    try {
      const response = await fetch(
        `/api/interview/schedule?candidate_id=${candidateId}&level=${currentLevel}`
      );
      const data = await response.json();

      // Ensure we always set an array
      if (data.scheduled_interviews && Array.isArray(data.scheduled_interviews)) {
        setScheduledInterviews(data.scheduled_interviews);
      } else {
        setScheduledInterviews([]);
      }
    } catch (error) {
      console.error("Failed to fetch scheduled interviews:", error);
      setScheduledInterviews([]); // Set empty array on error
    }
  };

  // Check for interview status updates from external system
  // const checkInterviewStatusUpdates = async () => {
  //   if (!candidateId) return;

  //   setStatusSyncLoading(true);
  //   try {
  //     const response = await fetch(
  //       `/api/interview/summary?candidate_id=${candidateId}&level=${currentLevel}`
  //     );
  //     const data = await response.json();

  //     if (data.external_feedback && data.has_external_feedback) {
  //       // Process external feedback and update status
  //       const feedback = data.external_feedback;

  //       if (feedback.status === "completed" && feedback.decision) {
  //         // Auto-update interview status
  //         await updateInterviewStatus(
  //           feedback.level,
  //           feedback.decision,
  //           feedback.feedback_data
  //         );

  //         showToast(
  //           `✅ ${feedback.level.toUpperCase()} Interview Completed: ${feedback.decision.toUpperCase()}`,
  //           feedback.decision === "passed" ? "success" : "warning"
  //         );
  //       }
  //     }
  //   } catch (error) {
  //     console.error("Status sync failed:", error);
  //   } finally {
  //     setStatusSyncLoading(false);
  //   }
  // };

  // Update interview status based on external feedback
  // const updateInterviewStatus = async (
  //   level: string,
  //   decision: string,
  //   feedbackData?: any
  // ) => {
  //   try {
  //     const response = await fetch("/api/interview/summary", {
  //       method: "POST",
  //       headers: {
  //         "Content-Type": "application/json",
  //       },
  //       body: JSON.stringify({
  //         candidate_id: candidateId,
  //         level,
  //         status: "completed",
  //         decision,
  //         feedback_data: feedbackData,
  //       }),
  //     });

  //     if (response.ok) {
  //       // Refresh interview data to show updated status
  //       await fetchCandidateDetails();

  //       // Auto-unlock next level if passed
  //       if (decision === "passed") {
  //         const nextLevel = parseInt(level.replace("level", "")) + 1;
  //         if (nextLevel <= 4) {
  //           setCurrentLevel(nextLevel);
  //         }
  //       }
  //     }
  //   } catch (error) {
  //     console.error("Failed to update interview status:", error);
  //   }
  // };

  // Enhanced scheduling functions for Level 2+ interviews
  const handleScheduleInterview = async (level: number) => {
    const levelStr = `level${level}`;

    // Validate scheduling data
    const errors: { [key: string]: string } = {};

    if (!schedulingData.interviewer_id) {
      errors.interviewer = "Please select an interviewer";
    }

    if (!schedulingData.scheduled_date) {
      errors.date = "Please select a date";
    }

    if (!schedulingData.scheduled_time) {
      errors.time = "Please select a time";
    }

    if (Object.keys(errors).length > 0) {
      setSchedulingErrors(errors);
      return;
    }

    setSchedulingLoading(true);
    setSchedulingErrors({});

 const roundNameMap = {
  level2: "Technical",
  level3: "Management",
  level4: "HR",
};

const roundName = roundNameMap[levelStr] || levelStr;

const payload = {
  candidateid: candidateId,
  roundName,
  interviewer: schedulingData.interviewer_name,
  intervieweremail: schedulingData.interviewer_id,
  scheduledAt: `${schedulingData.scheduled_date.toISOString().split('T')[0]}T${schedulingData.scheduled_time}:00.000Z`,
  notes: schedulingData.notes,
};
    try {
      // Schedule the interview using the technical interview API
      const scheduleResponse = await fetch("/api/interview/technicalinterview", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!scheduleResponse.ok) {
        throw new Error("Failed to schedule interview");
      }

      const scheduleData = await scheduleResponse.json();

      // Send email notification to both candidate and interviewer
      try {
        await fetch("/api/interview/scheduleInterview", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            candidate_id: candidateId,
            candidate_name: candidate?.name || "Unknown",
            candidate_email: candidate?.email || "",
            interviewer_id: schedulingData.interviewer_id,
            interviewer_name: schedulingData.interviewer_name,
            interviewer_email: schedulingData.interviewer_id,
            level: "Technical Interview",
            scheduled_date: schedulingData.scheduled_date.toISOString().split("T")[0],
            scheduled_time: schedulingData.scheduled_time,
            timezone: schedulingData.timezone,
            job_title: "Software Engineer",
          }),
        });
      } catch (emailError) {
        console.warn("Email notification failed:", emailError);
        // Don't fail the whole process if email fails
      }

      // Update UI
      await fetchScheduledInterviews();
      setShowSchedulingForm(false);
      setCurrentSchedulingLevel(null);
      setCurrentLevel(level);

      // Reset form
      setSchedulingData({
        interviewer_id: "",
        interviewer_name: "",
        scheduled_date: new Date(),
        scheduled_time: "",
        timezone: "Asia/Kolkata",
        notes: "",
      });

      showToast("Interview scheduled successfully!", "success");

      if (level === 2) {
        // Refresh technical interview data for Level 2
        const technicalinterviewData = await fetch(
          `/api/interview/technicalinterview?candidate_id=${candidateId}&roundName=Technical`
        );
        const technicalinterviewDetails = await technicalinterviewData.json();
        setTechnicalInterviewScheduled(technicalinterviewDetails);
      }
    } catch (error) {
      console.error("Scheduling error:", error);
      showToast("Failed to schedule interview. Please try again.", "error");
    } finally {
      setSchedulingLoading(false);
    }
  };

  const handleStartInterview = async (level: number) => {
    try {
      // Find the scheduled interview for this level
      const scheduledInterview = scheduledInterviews.find(
        interview => interview.level === `level${level}` && interview.status === 'scheduled'
      );

      if (!scheduledInterview) {
        showToast("No scheduled interview found for this level", "error");
        return;
      }

      // Update interview status to "in_progress"
      const updateResponse = await fetch("/api/interview/schedule", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: scheduledInterview.id,
          status: "in_progress",
        }),
      });

      if (!updateResponse.ok) {
        throw new Error("Failed to start interview");
      }

      // Refresh data
      await fetchScheduledInterviews();
      await fetchCandidateDetails();

      showToast("Interview started successfully!", "success");
    } catch (error) {
      console.error("Start interview error:", error);
      showToast("Failed to start interview. Please try again.", "error");
    }
  };

  const handleCancelInterview = async (scheduleId: string) => {
    if (!confirm("Are you sure you want to cancel this interview?")) {
      return;
    }

    try {
      const response = await fetch(`/api/interview/schedule?id=${scheduleId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        await fetchScheduledInterviews();
        showToast("Interview cancelled successfully!", "success");
      }
    } catch (error) {
      console.error("Cancel error:", error);
      showToast("Failed to cancel interview.", "error");
    }
  };

  // Add missing handleCompleteInterview function
  const handleCompleteInterview = async (levelId: number) => {
    try {
      // Find the scheduled interview for this level that is in progress
      const scheduledInterview = scheduledInterviews.find(
        interview =>
          interview.level === `level${levelId}` &&
          interview.status === "in_progress"
      );

      if (!scheduledInterview) {
        showToast("No in-progress interview found for this level", "error");
        return;
      }

      // Update interview status to "Completed"
      const updateResponse = await fetch("/api/interview/schedule", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: scheduledInterview.id,
          status: "Completed",
        }),
      });

      if (!updateResponse.ok) {
        throw new Error("Failed to complete interview");
      }

      // Refresh data
      await fetchScheduledInterviews();
      await fetchCandidateDetails();

      showToast("Interview marked as completed!", "success");
    } catch (error) {
      console.error("Complete interview error:", error);
      showToast("Failed to complete interview. Please try again.", "error");
    }
  };

  const handleCancelTechnicalInterview = async (interviewId: string) => {
    if (!confirm("Are you sure you want to cancel this technical interview?")) {
      return;
    }

    try {
      setCancelingInterview(true);
      const response = await fetch(`/api/interview/technicalinterview?id=${interviewId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        // Refresh the technical interview data
        const technicalinterviewData = await fetch(
          `/api/interview/technicalinterview?candidate_id=${candidateId}&roundName=Technical`
        );
        const technicalinterviewDetails = await technicalinterviewData.json();
        setTechnicalInterviewScheduled(technicalinterviewDetails);
        
        showToast("Technical interview cancelled successfully!", "success");
      } else {
        throw new Error("Failed to cancel interview");
      }
    } catch (error) {
      console.error("Cancel technical interview error:", error);
      showToast("Failed to cancel interview.", "error");
    } finally {
      setCancelingInterview(false);
    }
  };

  useEffect(() => {
    if (candidateId) {
      fetchCandidateDetails();
      fetchInterviewers();
      fetchScheduledInterviews();
      // checkInterviewStatusUpdates();
    }
  }, [candidateId]);

  const handleLevelProgression = async (newLevel: number) => {
    if (newLevel === 2 && !selectedInterviewer) {
      alert("Please select an interviewer for the Technical Interview");
      return;
    }

    try {
      setLevelUpdateLoading(true);

      // Create interview record for the new level
      const interviewData = {
        candidate_id: candidateId,
        level: `level${newLevel}`,
        status: "scheduled",
        interviewer: newLevel === 2 ? selectedInterviewer : null,
        start_time: new Date().toISOString(),
        end_time: null,
        decision: "pending",
        interviewnotes: "",
        is_ai_interview: newLevel === 1,
        panel: newLevel === 2 ? selectedInterviewer : "System",
        jobdescid: candidate?.Job_id || "",
        summary: `Level ${newLevel} interview scheduled`,
        timestamp: new Date().toISOString(),
        video: null,
      };

      // Post to interview API
      const response = await fetch("/api/interview", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(interviewData),
      });

      if (!response.ok) {
        throw new Error("Failed to create interview record");
      }

      setCurrentLevel(newLevel);

      // Refresh candidate data to show updated interview history
      await fetchCandidateDetails();

      alert(`Successfully progressed candidate to Level ${newLevel}`);
    } catch (err) {
      console.error("Error updating interview level:", err);
      alert("Failed to update interview level. Please try again.");
    } finally {
      setLevelUpdateLoading(false);
    }
  };

  const handleLevelChange = async (targetLevel: number) => {
    if (targetLevel === currentLevel) return;

    const confirmMessage =
      targetLevel > currentLevel
        ? `Are you sure you want to progress to Level ${targetLevel}?`
        : `Are you sure you want to go back to Level ${targetLevel}?`;

    if (!confirm(confirmMessage)) return;

    try {
      setLevelUpdateLoading(true);
      setCurrentLevel(targetLevel);

      // Here you could add API call to update the candidate's current level in the database
      console.log(`Changed candidate level to ${targetLevel}`);

      alert(`Successfully changed level to ${targetLevel}`);
    } catch (err) {
      console.error("Error changing level:", err);
      alert("Failed to change level. Please try again.");
    } finally {
      setLevelUpdateLoading(false);
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "hired":
        return "success";
      case "offer":
        return "success";
      case "interview":
        return "warning";
      case "screening":
        return "warning";
      case "rejected":
        return "danger";
      default:
        return "default";
    }
  };

  const getLevelStatusVariant = (levelId: number) => {
    if (levelId < currentLevel) return "success";
    if (levelId === currentLevel) return "warning";
    return "default";
  };

  // Level 1 AI Interview management functions
  const handleConfirmAndSendInterviewLink = async () => {
    if (!candidate) return;

    setConfirmingInterview(true);
    try {
      console.log(
        process.env.NEXT_PUBLIC_INTERVIEW_PANEL_LINK,
        "<<<<<<< INTERVIEW_PANEL_LINK >>>>>>"
      );

      const interviewLink = `${process.env.NEXT_PUBLIC_INTERVIEW_PANEL_LINK}?job_id=${candidate.Job_id}&candidate_id=${candidate.candidateid}`;

      // Update interview_date field with confirmed date
      const updateResponse = await fetch("/api/candidate", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          candidateid: candidate.candidateid,
          interview_date: candidate.scheduled_date,
        }),
      });

      if (!updateResponse.ok) {
        throw new Error("Failed to update interview date");
      }

      // Update interview_date field with confirmed date and AI interview details
      const updateResponseAI = await fetch("/api/ai-scheduling", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          candidate_id: candidate.candidateid,
          status: "Scheduled",
          level: "level1",
          is_ai_interview: true,
          panel: "AI",
          start_time: candidate.scheduled_date,
          end_time: "",
          decision: "pending",
          interviewnotes: "",
          video: "",
          summary: "",
          timestamp: new Date().toISOString(),
        }),
      });

      if (!updateResponseAI.ok) {
        throw new Error("Failed to update interview date");
      }

      // Send interview link email
      const emailData = {
        candidateId: candidate.candidateid,
        firstName: candidate.name,
        email: candidate.email,
        jobId: candidate.Job_id, // You may want to fetch this from job data
        interviewLink: interviewLink,
        scheduledDate: candidate.scheduled_date,
        timeSlot: candidate.interview_time_slot,
        interviewType: "AI Interview - Level 1",
      };

      const emailResponse = await fetch("/api/email/interview-link", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(emailData),
      });

      if (!emailResponse.ok) {
        throw new Error("Failed to send interview link email");
      }

      // Refresh candidate data
      await fetchCandidateDetails();

      showToast("Interview link sent successfully!", "success");
    } catch (error) {
      console.error("Error confirming interview:", error);
      showToast("Failed to send interview link. Please try again.", "error");
    } finally {
      setConfirmingInterview(false);
    }
  };

  console.log(confirmingInterview, "<<<<<<< confirmingInterview >>>>>>");

  console.log(rescheduleData, "rescheduleData>>>>>");

  const handleRescheduleInterview = async () => {
    if (!candidate && rescheduleData.reason) {
      showToast("Please fill in all reschedule details", "error");
      return;
    }

    setReschedulingInterview(true);
    try {
      // Reset form_responded to false and update candidate
      const updateResponse = await fetch("/api/candidate/update-status", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          candidateid: candidate?.candidateid,
          form_responded: false,
          scheduled_date: "",
          interview_time_slot: "",
          form_responded_at: "",
        }),
      });

      console.log(updateResponse, "updateResponse");

      if (!updateResponse.ok) {
        throw new Error("Failed to update candidate status");
      }

      // Send reschedule email
      const emailData = {
        candidateId: candidate?.candidateid,
        firstName: candidate?.name,
        email: candidate?.email,
        jobTitleId: candidate?.Job_id,
        originalDate: candidate?.scheduled_date,
        originalTimeSlot: candidate?.interview_time_slot,
        reason: rescheduleData?.reason,
        scheduleformLink: `${process.env.NEXT_PUBLIC_INTERVIEW_SCHEDULING_LINK}/${candidate?.candidateid}`,
      };

      const emailResponse = await fetch("/api/email/reschedule", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(emailData),
      });

      if (!emailResponse.ok) {
        throw new Error("Failed to send reschedule email");
      }

      // Reset form and refresh data
      // setRescheduleData({ reason: "" });
      setShowRescheduleForm(false);
      await fetchCandidateDetails();

      showToast("Interview rescheduled successfully!", "success");
    } catch (error) {
      console.error("Error rescheduling interview:", error);
      showToast("Failed to reschedule interview. Please try again.", "error");
    } finally {
      setReschedulingInterview(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="arrow-left" size="sm" className="mr-2" />
            Back
          </Button>
        </div>

        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">Loading candidate details...</span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !candidate) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="arrow-left" size="sm" className="mr-2" />
            Back
          </Button>
        </div>

        <Card padding="lg">
          <div className="text-center py-8">
            <Icon
              name="alert"
              size="xl"
              className="text-red-500 mx-auto mb-4"
            />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Candidate Not Found
            </h3>
            <p className="text-gray-600 mb-4">
              {error ||
                "The candidate you are looking for does not exist or has been removed."}
            </p>
            <div className="space-x-4">
              <Button variant="outline" onClick={() => router.back()}>
                Go Back
              </Button>
              <Button
                variant="primary"
                onClick={() => router.push("/candidates")}
              >
                View All Candidates
              </Button>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  console.log(rescheduleData.reason, "rescheduleData>>>");

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="arrow-left" size="sm" className="mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {candidate.name}
            </h1>
            <p className="text-gray-600">
              {candidate.email} • {candidate.current_location}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <Badge variant={getStatusVariant(candidate.status)} size="lg">
            {candidate.status.charAt(0).toUpperCase() +
              candidate.status.slice(1)}
          </Badge>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Interview Level Management */}
          <Card title="Interview Progress" padding="lg">
            {/* Level Management Controls */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-700">
                  Quick Level Management
                </span>
                <div className="flex space-x-2">
                  {INTERVIEW_LEVELS.map((level) => (
                    <Button
                      key={level.id}
                      variant={
                        level.id === currentLevel ? "primary" : "outline"
                      }
                      size="sm"
                      onClick={() => handleLevelChange(level.id)}
                      disabled={levelUpdateLoading}
                      className="min-w-16"
                    >
                      L{level.id}
                    </Button>
                  ))}
                </div>
              </div>
              <p className="text-xs text-gray-600">
                Click on a level to jump directly to that stage. Current: Level{" "}
                {currentLevel}
              </p>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">
                  Overall Progress
                </span>
                <span className="text-sm text-gray-500">
                  {Math.round(((currentLevel - 1) / 3) * 100)}% Complete
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${((currentLevel - 1) / 3) * 100}%` }}
                ></div>
              </div>
            </div>

            <div className="space-y-4">
              {INTERVIEW_LEVELS.map((level, index) => {
                const isCompleted = level.id < currentLevel;
                const isCurrent = level.id === currentLevel;
                const isPending = level.id > currentLevel;

                return (
                  <div
                    key={level.id}
                    className={cn(
                      "p-4 rounded-lg border-2 transition-all duration-200",
                      isCurrent && "border-blue-500 bg-blue-50",
                      isCompleted && "border-green-500 bg-green-50",
                      isPending && "border-gray-300 bg-gray-50"
                    )}
                  >
                    {/* Connection line to next level */}
                    {index < INTERVIEW_LEVELS.length - 1 && (
                      <div
                        className={`absolute left-6 top-16 w-0.5 h-4 ${
                          isCompleted ? "bg-green-500" : "bg-gray-300"
                        }`}
                      ></div>
                    )}

                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                            isCompleted
                              ? "bg-green-500 text-white"
                              : isCurrent
                              ? "bg-blue-500 text-white"
                              : "bg-gray-300 text-gray-600"
                          }`}
                        >
                          {isCompleted ? (
                            <Icon name="check" size="sm" />
                          ) : (
                            level.id
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">
                            {level.name}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {level.description}
                          </p>

                          {/* Level 1 AI Interview - Enhanced UI */}
                          {level.id === 1 && candidate && (
                            <div className="mt-3 space-y-2">
                              {candidate.form_responded ? (
                                <div className="bg-white p-3 rounded-lg border border-gray-200">
                                  <div className="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                      <span className="font-medium text-gray-700">
                                        Scheduled Date:
                                      </span>
                                      <p className="text-gray-900">
                                        {candidate.scheduled_date}
                                      </p>
                                    </div>
                                    <div>
                                      <span className="font-medium text-gray-700">
                                        Time Slot:
                                      </span>
                                      <p className="text-gray-900">
                                        {candidate.interview_time_slot}
                                      </p>
                                    </div>
                                    <div className="">
                                      <span className="font-medium text-gray-700">
                                        Response Received:
                                      </span>
                                      <p className="text-gray-900">
                                        {candidate.form_responded_at
                                          ? formatDateConsistent(
                                              candidate.form_responded_at
                                            )
                                          : "Recently"}
                                      </p>
                                    </div>
                                    <div>
                                      <span className="font-medium text-gray-700">
                                        Interview Status:{" "}
                                      </span>
                                      <p
                                        className={`px-2 py-1 rounded-full text-sm inline-block ${
                                          candidate.interview_date
                                            ? "text-green-800 bg-green-100"
                                            : "text-red-800 bg-red-100"
                                        }`}
                                      >
                                        {candidate.interview_date
                                          ? "scheduled"
                                          : "not scheduled"}
                                      </p>
                                    </div>
                                  </div>

                                  {/* Interview Completion Section - Only show when interview is completed */}
                                  {candidate.interview_eval_details?.some(
                                    (interview: any) =>
                                      interview.level === "level1" &&
                                      interview.status === "Completed" &&
                                      interview.is_ai_interview
                                  ) && (
                                    <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                      <div className="flex items-center space-x-2 mb-3">
                                        <Icon
                                          name="check-circle"
                                          size="sm"
                                          className="text-blue-600"
                                        />
                                        <h4 className="font-medium text-blue-900">
                                          Interview Completed
                                        </h4>
                                        <Badge variant="success" size="sm">
                                          AI Interview Done
                                        </Badge>
                                      </div>

                                      {/* Interview Notes */}
                                      <div className="mb-4">
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                          AI Interview Summary:
                                        </label>
                                        <div className="bg-white p-3 rounded border border-gray-200 text-sm text-gray-700 leading-relaxed">
                                          {candidate.interview_eval_details.find(
                                            (interview: any) =>
                                              interview.level === "level1" &&
                                              interview.is_ai_interview
                                          )?.interviewnotes ||
                                            "No summary available"}
                                        </div>
                                      </div>

                                      {/* Interview Timestamp */}
                                      <div className="mb-4">
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                          Completed On:
                                        </label>
                                        <p className="text-sm text-gray-900">
                                          {formatDateConsistent(
                                            candidate.interview_eval_details.find(
                                              (interview: any) =>
                                                interview.level === "level1" &&
                                                interview.is_ai_interview
                                            )?.timestamp
                                          )}
                                        </p>
                                      </div>

                                      {/* HR Decision Section - Only show if decision is still pending */}
                                      {candidate.interview_eval_details.find(
                                        (interview: any) =>
                                          interview.level === "level1" &&
                                          interview.is_ai_interview
                                      )?.decision === "pending" && (
                                        <div className="border-t border-blue-200 pt-4">
                                          <h5 className="font-medium text-gray-900 mb-3">
                                            HR Decision Required
                                          </h5>

                                          <div className="space-y-4">
                                            {/* Decision Selection */}
                                            <div>
                                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Interview Decision{" "}
                                                <span className="text-red-500">
                                                  *
                                                </span>
                                              </label>
                                              <div className="flex space-x-4">
                                                <label className="flex items-center">
                                                  <input
                                                    type="radio"
                                                    name="aiInterviewDecision"
                                                    value="pass"
                                                    checked={
                                                      aiInterviewDecision ===
                                                      "pass"
                                                    }
                                                    onChange={(e) =>
                                                      setAiInterviewDecision(
                                                        e.target.value
                                                      )
                                                    }
                                                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                                                  />
                                                  <span className="ml-2 text-sm text-gray-700">
                                                    <span className="font-medium text-green-600">
                                                      Pass
                                                    </span>{" "}
                                                    - Move to next round
                                                  </span>
                                                </label>
                                                <label className="flex items-center">
                                                  <input
                                                    type="radio"
                                                    name="aiInterviewDecision"
                                                    value="fail"
                                                    checked={
                                                      aiInterviewDecision ===
                                                      "fail"
                                                    }
                                                    onChange={(e) =>
                                                      setAiInterviewDecision(
                                                        e.target.value
                                                      )
                                                    }
                                                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                                                  />
                                                  <span className="ml-2 text-sm text-gray-700">
                                                    <span className="font-medium text-red-600">
                                                      Fail
                                                    </span>{" "}
                                                    - Reject candidate
                                                  </span>
                                                </label>
                                              </div>
                                            </div>

                                            {/* Summary/Reason Section */}
                                            <div>
                                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                                {aiInterviewDecision === "pass"
                                                  ? "Why are you passing this candidate?"
                                                  : aiInterviewDecision ===
                                                    "fail"
                                                  ? "Reason for rejection:"
                                                  : "Decision Summary:"}{" "}
                                                <span className="text-red-500">
                                                  *
                                                </span>
                                              </label>
                                              <textarea
                                                value={aiInterviewSummary}
                                                onChange={(e) =>
                                                  setAiInterviewSummary(
                                                    e.target.value
                                                  )
                                                }
                                                placeholder={
                                                  aiInterviewDecision === "pass"
                                                    ? "Explain the candidate's strengths and why they should proceed to the next round..."
                                                    : aiInterviewDecision ===
                                                      "fail"
                                                    ? "Provide specific reasons for rejection..."
                                                    : "Enter your decision summary..."
                                                }
                                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"
                                                rows={3}
                                                required
                                              />
                                              <p className="text-xs text-gray-500 mt-1">
                                                This summary will be used for
                                                record-keeping and future
                                                reference.
                                              </p>
                                            </div>

                                            {/* Action Buttons */}
                                            <div className="flex space-x-3">
                                              <Button
                                                variant="primary"
                                                size="sm"
                                                onClick={
                                                  handleAiInterviewDecision
                                                }
                                                loading={submittingDecision}
                                                disabled={
                                                  !aiInterviewDecision ||
                                                  !aiInterviewSummary.trim()
                                                }
                                                className={`${
                                                  aiInterviewDecision === "pass"
                                                    ? "bg-green-600 hover:bg-green-700"
                                                    : aiInterviewDecision ===
                                                      "fail"
                                                    ? "bg-red-600 hover:bg-red-700"
                                                    : ""
                                                }`}
                                              >
                                                <Icon
                                                  name={
                                                    aiInterviewDecision ===
                                                    "pass"
                                                      ? "arrow-right"
                                                      : "x"
                                                  }
                                                  size="sm"
                                                  className="mr-1"
                                                />
                                                {aiInterviewDecision === "pass"
                                                  ? "Pass & Move to Level 2"
                                                  : aiInterviewDecision ===
                                                    "fail"
                                                  ? "Reject Candidate"
                                                  : "Submit Decision"}
                                              </Button>
                                              <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => {
                                                  setAiInterviewDecision("");
                                                  setAiInterviewSummary("");
                                                }}
                                                disabled={submittingDecision}
                                              >
                                                Reset
                                              </Button>
                                            </div>
                                          </div>
                                        </div>
                                      )}

                                      {/* Show Final Decision if already made */}
                                      {candidate.interview_eval_details.find(
                                        (interview: any) =>
                                          interview.level === "level1" &&
                                          interview.is_ai_interview
                                      )?.decision !== "pending" && (
                                        <div className="border-t border-blue-200 pt-4">
                                          <div className="flex items-center justify-between">
                                            <h5 className="font-medium text-gray-900">
                                              Final Decision
                                            </h5>
                                            <Badge
                                              variant={
                                                candidate.interview_eval_details.find(
                                                  (interview: any) =>
                                                    interview.level ===
                                                      "level1" &&
                                                    interview.is_ai_interview
                                                )?.decision === "pass"
                                                  ? "success"
                                                  : "danger"
                                              }
                                              size="sm"
                                            >
                                              {candidate.interview_eval_details
                                                .find(
                                                  (interview: any) =>
                                                    interview.level ===
                                                      "level1" &&
                                                    interview.is_ai_interview
                                                )
                                                ?.decision?.toUpperCase()}
                                            </Badge>
                                          </div>
                                          {candidate.interview_eval_details.find(
                                            (interview: any) =>
                                              interview.level === "level1" &&
                                              interview.is_ai_interview
                                          )?.summary && (
                                            <div className="mt-2 p-3 bg-gray-50 rounded border text-sm text-gray-700">
                                              {
                                                candidate.interview_eval_details.find(
                                                  (interview: any) =>
                                                    interview.level ===
                                                      "level1" &&
                                                    interview.is_ai_interview
                                                )?.summary
                                              }
                                            </div>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                  )}

                                  {/* Original Action Buttons - Only show if interview not completed */}
                                  {!candidate.interview_eval_details?.some(
                                    (interview: any) =>
                                      interview.level === "level1" &&
                                      interview.status === "Completed" &&
                                      interview.is_ai_interview
                                  ) && (
                                    <div className="flex space-x-2 mt-3">
                                      <Button
                                        size="sm"
                                        onClick={
                                          handleConfirmAndSendInterviewLink
                                        }
                                        loading={confirmingInterview}
                                        disabled={
                                          Boolean(candidate.interview_date) ||
                                          reschedulingInterview
                                        }
                                      >
                                        <Icon
                                          name="send"
                                          size="sm"
                                          className="mr-1"
                                        />
                                        Confirm & Send Interview Link
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() =>
                                          setShowRescheduleForm(true)
                                        }
                                        disabled={
                                          confirmingInterview ||
                                          reschedulingInterview
                                        }
                                      >
                                        <Icon
                                          name="calendar"
                                          size="sm"
                                          className="mr-1"
                                        />
                                        Reschedule Interview
                                      </Button>
                                    </div>
                                  )}
                                </div>
                              ) : (
                                <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
                                  <div className="flex items-center space-x-2">
                                    <Icon
                                      name="clock"
                                      size="sm"
                                      className="text-yellow-600"
                                    />
                                    <span className="text-sm text-yellow-800">
                                      Waiting for candidate response to
                                      scheduling form
                                    </span>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Action buttons section - moved to the right */}
                      <div className="flex flex-col items-end space-y-2">
                        {/* Existing level management buttons for other levels */}
                        {level.id > 1 && (
                          <div className="space-y-3">
                            {isCurrent && (
                              <>
                                {level.type === "manual" && (
                                  <div className="space-y-3">
                                    {/* Check if interview is scheduled for this level */}
                                    {Array.isArray(scheduledInterviews) && scheduledInterviews.some(interview => 
                                      interview.level === `level${level.id}` && 
                                      ['scheduled', 'in_progress'].includes(interview.status)
                                    ) ? (
                                      // Interview is scheduled - show details and actions
                                      <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                                        {(() => {
                                          const scheduledInterview = scheduledInterviews.find(interview => 
                                            interview.level === `level${level.id}` && 
                                            ['scheduled', 'in_progress'].includes(interview.status)
                                          );
                                          return (
                                            <div className="space-y-3">
                                              <div className="flex items-center justify-between">
                                                <h5 className="font-medium text-gray-900">Interview Scheduled</h5>
                                                <Badge 
                                                  variant={scheduledInterview?.status === 'in_progress' ? 'warning' : 'info'}
                                                  size="sm"
                                                >
                                                  {scheduledInterview?.status === 'in_progress' ? 'In Progress' : 'Scheduled'}
                                                </Badge>
                                              </div>
                                              
                                              <div className="grid grid-cols-2 gap-4 text-sm">
                                                <div>
                                                  <span className="font-medium text-gray-700">Date:</span>
                                                  <p className="text-gray-900">{scheduledInterview?.scheduled_date}</p>
                                                </div>
                                                <div>
                                                  <span className="font-medium text-gray-700">Time:</span>
                                                  <p className="text-gray-900">{scheduledInterview?.scheduled_time}</p>
                                                </div>
                                                <div className="col-span-2">
                                                  <span className="font-medium text-gray-700">Interviewer:</span>
                                                  <p className="text-gray-900">{scheduledInterview?.interviewer_name}</p>
                                                </div>
                                              </div>

                                              <div className="flex space-x-2">
                                                {scheduledInterview?.status === 'scheduled' && (
                                                  <>
                                                    <Button
                                                      size="sm"
                                                      onClick={() => handleStartInterview(level.id)}
                                                      className="min-h-[44px]"
                                                    >
                                                      <Icon name="play" size="sm" className="mr-1" />
                                                      Start Interview
                                                    </Button>
                                                    <Button
                                                      size="sm"
                                                      variant="outline"
                                                      onClick={() => handleCancelInterview(scheduledInterview?.id)}
                                                      className="min-h-[44px]"
                                                    >
                                                      <Icon name="x" size="sm" className="mr-1" />
                                                      Cancel Schedule
                                                    </Button>
                                                  </>
                                                )}
                                                
                                                {scheduledInterview?.status === 'in_progress' && (
                                                  <Button
                                                    size="sm"
                                                    onClick={() => handleCompleteInterview(level.id)}
                                                    className="min-h-[44px]"
                                                  >
                                                    <Icon name="check" size="sm" className="mr-1" />
                                                    Complete Interview
                                                  </Button>
                                                )}
                                              </div>
                                            </div>
                                          );
                                        })()}
                                      </div>
                                    ) : (
                                      // No interview scheduled - show schedule button
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => {
                                          console.log('Schedule button clicked', { 
                                            showSchedulingForm, 
                                            currentSchedulingLevel, 
                                            levelId: level.id 
                                          });
                                          if (!showSchedulingForm || currentSchedulingLevel !== level.id) {
                                            setCurrentSchedulingLevel(level.id);
                                            setShowSchedulingForm(true);
                                            setSchedulingData({
                                              interviewer_id: "",
                                              interviewer_name: "",
                                              scheduled_date: new Date(),
                                              scheduled_time: "",
                                              timezone: "Asia/Kolkata",
                                              notes: "",
                                            });
                                          } else {
                                            setShowSchedulingForm(false);
                                            setCurrentSchedulingLevel(null);
                                            setSchedulingErrors({});
                                          }
                                        }}
                                        className="min-h-[44px] w-full sm:w-auto"
                                      >
                                        <Icon name="calendar" size="sm" className="mr-1" />
                                        {showSchedulingForm && currentSchedulingLevel === level.id ? 'Cancel Schedule' : 'Schedule'}
                                      </Button>
                                    )}
                                  </div>
                                )}
                              </>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Scheduling Form - Positioned after the main content row */}
                    {level.id > 1 && isCurrent && level.type === "manual" && 
                     showSchedulingForm && currentSchedulingLevel === level.id && (
                      <div className="mt-4 space-y-4 pl-2 sm:pl-4 border-l-2 border-blue-200 bg-blue-50/30 py-3 sm:py-4 rounded-r-lg bg-white p-2 text-black">
                        <div className="text-sm font-medium text-gray-800 mb-2">
                          Schedule Level {level.id} Interview
                        </div>
                        
                        {/* Interviewer Selection - Vertical Layout */}
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            Interviewer <span className="text-red-500">*</span>
                          </label>
                          <select
                            value={schedulingData.interviewer_id}
                            onChange={(e) => {
                              const selectedInterviewer = technicalInterviewDetails?.find(
                                (interviewer: any) => interviewer.email === e.target.value
                              );
                              setSchedulingData(prev => ({
                                ...prev,
                                interviewer_id: e.target.value,
                                interviewer_name: selectedInterviewer?.name || "",
                              }));
                            }}
                            className="w-full px-3 py-2.5 sm:py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm min-h-[44px] sm:min-h-0"
                          >
                            <option value="">Select interviewer</option>
                            {Array.isArray(technicalInterviewDetails) && technicalInterviewDetails.map((interviewer, index) => (
                              <option key={index} value={interviewer.email}>
                                {interviewer.name} - {interviewer.designation}
                              </option>
                            ))}
                          </select>
                          {schedulingErrors.interviewer && (
                            <p className="text-xs text-red-600">{schedulingErrors.interviewer}</p>
                          )}
                        </div>

                        {/* Date and Time Selection - Vertical Layout */}
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            Date & Time <span className="text-red-500">*</span>
                          </label>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                            <div>
                              <label className="block text-xs text-gray-600 mb-1">Date</label>
                              <input
                                type="date"
                                value={schedulingData.scheduled_date.toISOString().split('T')[0]}
                                onChange={(e) => setSchedulingData(prev => ({
                                  ...prev,
                                  scheduled_date: new Date(e.target.value)
                                }))}
                                min={new Date().toISOString().split('T')[0]}
                                className="w-full px-3 py-2.5 sm:py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm min-h-[44px] sm:min-h-0"
                              />
                            </div>
                            <div>
                              <label className="block text-xs text-gray-600 mb-1">Time</label>
                              <select
                                value={schedulingData.scheduled_time}
                                onChange={(e) => setSchedulingData(prev => ({
                                  ...prev,
                                  scheduled_time: e.target.value
                                }))}
                                className="w-full px-3 py-2.5 sm:py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm min-h-[44px] sm:min-h-0"
                              >
                                <option value="">Select time</option>
                                <option value="09:00">9:00 AM</option>
                                <option value="09:30">9:30 AM</option>
                                <option value="10:00">10:00 AM</option>
                                <option value="10:30">10:30 AM</option>
                                <option value="11:00">11:00 AM</option>
                                <option value="11:30">11:30 AM</option>
                                <option value="12:00">12:00 PM</option>
                                <option value="14:00">2:00 PM</option>
                                <option value="14:30">2:30 PM</option>
                                <option value="15:00">3:00 PM</option>
                                <option value="15:30">3:30 PM</option>
                                <option value="16:00">4:00 PM</option>
                                <option value="16:30">4:30 PM</option>
                                <option value="17:00">5:00 PM</option>
                                <option value="17:30">5:30 PM</option>
                                <option value="18:00">6:00 PM</option>
                              </select>
                            </div>
                          </div>
                          
                          {/* Error messages for date/time */}
                          {(schedulingErrors.date || schedulingErrors.time) && (
                            <div className="space-y-1">
                              {schedulingErrors.date && (
                                <p className="text-xs text-red-600">{schedulingErrors.date}</p>
                              )}
                              {schedulingErrors.time && (
                                <p className="text-xs text-red-600">{schedulingErrors.time}</p>
                              )}
                            </div>
                          )}
                        </div>

                        {/* Notes - Vertical Layout */}
                        {/* <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            Notes
                          </label>
                          <textarea
                            value={schedulingData.notes}
                            onChange={(e) => setSchedulingData(prev => ({
                              ...prev,
                              notes: e.target.value
                            }))}
                            placeholder="Additional notes..."
                            className="w-full px-3 py-2.5 sm:py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm resize-none"
                            rows={2}
                          />
                        </div> */}

                        {/* Action Buttons - Vertical Layout */}
                        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 pt-2">
                          <Button
                            size="sm"
                            onClick={() => handleScheduleInterview(level.id)}
                            loading={schedulingLoading}
                            disabled={
                              !schedulingData.interviewer_id ||
                              !schedulingData.scheduled_date ||
                              !schedulingData.scheduled_time
                            }
                            className="min-h-[44px] w-full sm:w-auto"
                          >
                            <Icon name="check" size="sm" className="mr-1" />
                            Confirm Schedule
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setShowSchedulingForm(false);
                              setCurrentSchedulingLevel(null);
                              setSchedulingErrors({});
                            }}
                            disabled={schedulingLoading}
                            className="min-h-[44px] w-full sm:w-auto"
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Reschedule Form Modal */}
            {showRescheduleForm && (
              <div className="fixed inset-0 bg-gray-200 bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg p-6 w-full max-w-md text-black">
                  <h3 className="text-lg font-semibold mb-4">
                    Reschedule AI Interview
                  </h3>

                  <div className="space-y-4">
                    <FormField
                      label="Reason for Rescheduling"
                      name="reason"
                      type="textarea"
                      value={rescheduleData?.reason}
                      onChange={(name, value) =>
                        setRescheduleData((prev) => ({
                          ...prev,
                          [name]: value,
                        }))
                      }
                      placeholder="Optional: Explain why the interview needs to be rescheduled"
                    />
                  </div>

                  <div className="flex space-x-2 mt-6">
                    <Button
                      onClick={handleRescheduleInterview}
                      loading={reschedulingInterview}
                      // disabled={!rescheduleData[0]?.reason}
                    >
                      Send Reschedule Request
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowRescheduleForm(false);
                        setRescheduleData({ reason: "" });
                      }}
                      disabled={reschedulingInterview}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </Card>

          {/* Experience Summary */}
          <Card title="Experience Summary" padding="lg">
            <div className="prose max-w-none">
              <p className="text-gray-700 whitespace-pre-wrap">
                {candidate.experience_summary}
              </p>
            </div>
          </Card>

          {/* Employment History */}
          <Card title="Employment History" padding="lg">
            <div className="space-y-4">
              {candidate.employment_history.map((job, index) => (
                <div key={index} className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-medium text-gray-900">{job.role}</h4>
                  <p className="text-gray-600">{job.organization}</p>
                  <p className="text-sm text-gray-500">
                    {job.start_date} - {job.end_date}
                  </p>
                </div>
              ))}
            </div>
          </Card>

          {/* Education */}
          <Card title="Education" padding="lg">
            <div className="space-y-4">
              {candidate.education.map((edu, index) => (
                <div key={index} className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-medium text-gray-900">{edu.degree}</h4>
                  <p className="text-gray-600">{edu.field_of_study}</p>
                  {edu.institution && (
                    <p className="text-gray-600">{edu.institution}</p>
                  )}
                  <p className="text-sm text-gray-500">
                    {edu.start_date} - {edu.end_date}
                  </p>
                </div>
              ))}
            </div>
          </Card>
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Resume Preview */}
          <Card title="Resume" padding="lg">
            <div className="space-y-4">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setShowResumePreview(!showResumePreview)}
              >
                <Icon name="file" size="sm" className="mr-2" />
                {showResumePreview ? "Hide Preview" : "Show Preview"}
              </Button>
              <Button
                variant="primary"
                className="w-full"
                onClick={() =>
                  window.open(
                    `/api/download-resume?id=${candidate.fileid}`,
                    "_blank"
                  )
                }
              >
                <Icon name="download" size="sm" className="mr-2" />
                Download Resume
              </Button>

              {showResumePreview && (
                <div className="mt-4">
                  <div className="border rounded-lg overflow-hidden bg-gray-50">
                    <iframe
                      src={`/api/download-resume?id=${candidate.fileid}`}
                      className="w-full h-96"
                      title="Resume Preview"
                      onError={() => {
                        console.error("Failed to load resume preview");
                      }}
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-2 text-center">
                    If the preview doesn't load, click "Download Resume" to view
                    the file.
                  </p>
                </div>
              )}
            </div>
          </Card>

          {/* Candidate Info */}
          <Card title="Candidate Information" padding="lg">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Experience
                </label>
                <p className="text-gray-900">
                  {candidate.years_of_experience} years
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location
                </label>
                <p className="text-gray-900">{candidate.current_location}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Relocation
                </label>
                <p className="text-gray-900">
                  {candidate.flexible_with_relocation}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notice Period
                </label>
                <p className="text-gray-900">
                  {candidate.current_notice_period}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Current CTC
                </label>
                <p className="text-gray-900">{candidate.current_ctc}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Expected CTC
                </label>
                <p className="text-gray-900">{candidate.expected_ctc}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Applied Date
                </label>
                <p className="text-gray-900">
                  {formatDateConsistent(new Date(candidate.created_on))}
                </p>
              </div>
            </div>
          </Card>

          {/* Skills */}
          <Card title="Skills" padding="lg">
            <div className="flex flex-wrap gap-2">
              {candidate.key_skills.map((skill, index) => (
                <Badge key={index} variant="default" size="sm">
                  {skill}
                </Badge>
              ))}
            </div>
          </Card>

          {/* Achievements */}
          {candidate.achievements.length > 0 && (
            <Card title="Achievements" padding="lg">
              <ul className="space-y-2">
                {candidate.achievements.map((achievement, index) => (
                  <li key={index} className="text-sm text-gray-700">
                    • {achievement}
                  </li>
                ))}
              </ul>
            </Card>
          )}

          {/* Certifications */}
          {candidate.certifications.length > 0 && (
            <Card title="Certifications" padding="lg">
              <ul className="space-y-2">
                {candidate.certifications.map((cert, index) => (
                  <li key={index} className="text-sm text-gray-700">
                    • {cert}
                  </li>
                ))}
              </ul>
            </Card>
          )}

          {/* Actions */}
          <Card title="Actions" padding="lg">
            <div className="space-y-3">
              <Button variant="primary" className="w-full">
                <Icon name="mail" size="sm" className="mr-2" />
                Contact Candidate
              </Button>
              <Button variant="outline" className="w-full">
                <Icon name="calendar" size="sm" className="mr-2" />
                Schedule Interview
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.push("/candidates")}
              >
                <Icon name="list" size="sm" className="mr-2" />
                View All Candidates
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
