'use client';

import React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Icon } from '@/components/ui';
import { NavigationItem } from '@/types';

interface SidebarProps {
  items: NavigationItem[];
  className?: string;
  onItemClick?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ items, className, onItemClick }) => {
  const router = useRouter();
  const pathname = usePathname();

  const handleItemClick = (item: NavigationItem) => {
    console.log("navigation",item.href);
    if(item.href === '/api/auth/logout'){
      localStorage.clear();
       window.location.href = item.href; // ✅ Full redirect that Auth0 requires
        return;
    }else{
    router.push(item.href);
    }
    onItemClick?.(); // Close sidebar on mobile after navigation
  };

  const isActive = (href: string) => {
    // Handle exact match for dashboard
    if (href === '/dashboard' && pathname === '/dashboard') {
      return true;
    }
    // Handle other routes
    if (href !== '/dashboard' && pathname.startsWith(href)) {
      return true;
    }
    return false;
  };

  return (
    <div className={cn('w-64 bg-white border-r border-gray-200 h-full', className)}>
      {/* Logo/Brand */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">RP</span>
          </div>
          <span className="text-xl font-bold text-gray-900">RekruitPro AI</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="p-4">
        <ul className="space-y-2">
          {items.map((item) => {
            const active = isActive(item.href);
            return (
              <li key={item.id}>
                <button
                  onClick={() => handleItemClick(item)}
                  className={cn(
                    'w-full flex items-center justify-between px-3 py-3 text-md font-medium rounded-lg transition-colors',
                    active
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  )}
                >
                  <div className="flex items-center space-x-3">
                    <Icon
                      name={item.icon}
                      size="sm"
                      className={active ? 'text-blue-700' : 'text-gray-500'}
                    />
                    <span>{item.label}</span>
                  </div>
                  {item.badge && (
                    <span className="bg-red-100 text-red-800 text-xs font-medium px-2 py-0.5 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* User Profile Section */}
      {/* <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <span className="text-gray-600 text-sm font-medium">JD</span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">John Doe</p>
            <p className="text-xs text-gray-500 truncate">HR Manager</p>
          </div>
          <button className="text-gray-400 hover:text-gray-600">
            <Icon name="settings" size="sm" />
          </button>
        </div>
      </div> */}
    </div>
  );
};

export default Sidebar;
