'use client';

import React, { useEffect, useState } from 'react';
import { cn, formatRelativeTime, getStatusColor } from '@/lib/utils';
import { Card, Badge, Button, Icon } from '@/components/ui';
import { Job } from '@/types';

interface JobPostingCardProps {
  job: Job;
  onView?: (job: Job) => void;
  onEdit?: (job: Job) => void;
  onDelete?: (job: Job) => void;
  className?: string;
}

const JobPostingCard: React.FC<JobPostingCardProps> = ({
  job,
  onView,
  onEdit,
  onDelete,
  className,
}) => {
  const handleView = () => onView?.(job);
  const handleEdit = () => onEdit?.(job);
  const handleDelete = () => onDelete?.(job);

  const [formattedSalary, setFormattedSalary] = useState<string | null>(null);
  const [expanded, setExpanded] = useState(false);
  const contentRef = React.useRef<HTMLDivElement>(null);
  const [showReadMore, setShowReadMore] = useState(false);

  useEffect(() => {
    if (job.salary) {
      const formatted = `${job.salary.currency} ${job.salary.min.toLocaleString('en-US')} - ${job.salary.max.toLocaleString('en-US')}`;
      setFormattedSalary(formatted);
    }
  }, [job.salary]);

  useEffect(() => {
    if (contentRef.current) {
      // Check if the content overflows one line
      setShowReadMore(contentRef.current.scrollHeight > contentRef.current.clientHeight);
    }
  }, [job.description]);

  return (
    <Card className={cn(
      'transition-shadow rounded-2xl border border-gray-200 bg-white group hover:shadow-lg min-h-[110px] flex flex-col justify-between',
      job.status === 'active' ? 'border-green-300 shadow-green-100' : '',
      className
    )} padding="md">
      <div className="flex-1 flex flex-col">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-bold text-gray-900 mb-1 group-hover:text-blue-700 transition-colors truncate">
              {job.title}
            </h3>
            <p className="text-xs text-gray-500 mb-2 truncate">
              {job.department} • {job.location}
            </p>
            <div className="flex flex-wrap items-center gap-2 mb-1">
              <Badge 
                variant={job.status === 'active' ? 'success' : 
                        job.status === 'paused' ? 'warning' : 
                        job.status === 'closed' ? 'danger' : 'default'}
                size="sm"
              >
                {job.status}
              </Badge>
              <Badge variant="info" size="sm">
                {job.type}
              </Badge>
            </div>
          </div>
          <div className="flex items-center gap-1 ml-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleView}
              className="hover:bg-blue-50"
              aria-label="View"
            >
              <Icon name="eye" size="sm" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleEdit}
              className="hover:bg-yellow-50"
              aria-label="Edit"
            >
              <Icon name="edit" size="sm" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDelete}
              className="hover:bg-red-50"
              aria-label="Delete"
            >
              <Icon name="trash" size="sm" />
            </Button>
          </div>
        </div>
        <div className="mb-3 flex-1 relative">
          <div
            ref={contentRef}
            className={cn(
              'ql-editor text-sm text-gray-700 px-0 pt-0 pb-1 border-none bg-transparent transition-all',
              !expanded && 'line-clamp-1 h-[24px] min-h-[24px] max-h-[24px] overflow-hidden',
              expanded && 'min-h-[24px]' // keep height consistent when expanded
            )}
            style={{ padding: 0, border: 'none', background: 'none' }}
            dangerouslySetInnerHTML={{ __html: job.description }}
          />
          {showReadMore && !expanded && (
            <div className="absolute bottom-0 left-0 w-full flex justify-end pt-1">
              <button
                className="text-xs text-blue-600 font-semibold px-2 py-1 rounded hover:underline focus:outline-none bg-white"
                onClick={() => setExpanded(true)}
              >
                Read more
              </button>
            </div>
          )}
          {showReadMore && expanded && (
            <div className="w-full flex justify-end pt-1">
              <button
                className="text-xs text-blue-600 font-semibold px-2 py-1 rounded hover:underline focus:outline-none bg-white"
                onClick={() => setExpanded(false)}
              >
                Show less
              </button>
            </div>
          )}
          {formattedSalary && (
            <p className="text-xs font-medium text-gray-900 mt-1">
              {formattedSalary}
            </p>
          )}
        </div>
      </div>
      <div className="flex items-center justify-between text-xs text-gray-400 mt-2">
        <span>Posted {formatRelativeTime(job.postedDate)}</span>
        {job.closingDate && (
          <span>Closes {formatRelativeTime(job.closingDate)}</span>
        )}
      </div>
    </Card>
  );
};

export default JobPostingCard;
