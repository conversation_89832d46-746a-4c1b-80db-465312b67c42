import axios from "axios";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      candidateid,
      Priority,
      status,
      link_expiry_at,
      link_sent_at,
      interview_date,
      form_responded,
      scheduled_date,
      interview_time_slot,
      form_responded_at,
    } = body;

    console.log(  form_responded,
      scheduled_date,
      interview_time_slot,
      form_responded_at, "body>>>>>>>>>");

    if (!candidateid) {
      return NextResponse.json(
        { error: "Candidate ID is required" },
        { status: 400 }
      );
    }

    // candidateid: candidate?.candidateid,
    //       form_responded: false,
    //       scheduled_date: "",
    //       interview_time_slot: "",
    //       form_responded_at: "",

    const { data } = await axios.put(`${process.env.NODE_RED_URL}/candidate-schedule-reschedulelink`, {
        candidateid,
        form_responded,
        scheduled_date,
        interview_time_slot,
        form_responded_at,
     });
    console.log("data", data);

    return Response.json({ data });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation failed", errors: error.issues },
        { status: 400 },
      );
    }

    console.error("Unexpected error:", error);
    return NextResponse.json(
      { message: "An unexpected error occurred", error },
      { status: 500 }
    );
  }
}