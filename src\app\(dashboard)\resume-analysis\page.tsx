"use client";

import React, { useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>, Icon, Badge, DataTable, Dropdown } from "@/components";
import { log } from "console";
import axios from "axios";

interface Job {
  id: string;
  title: string;
  description: string;
}

// Processing step types
type ProcessingStep = "upload" | "extract" | "ai" | "complete" | "failed";

interface ProcessingStatus {
  step: ProcessingStep;
  message: string;
  completed: boolean;
}

// Analysis History Interface
interface AnalysisHistoryItem {
  id: string;
  fileName: string;
  candidateName: string;
  score: number;
  matchedSkills: string[];
  analyzedDate: string;
  analyzedDateRaw: string; // Raw date for sorting
  status: string;
  jobTitle?: string;
}

export default function ResumeAnalysisPage() {
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null);
  const [isAnalyzing, setIsAnalyzing] = React.useState(false);
  const [analysisResults, setAnalysisResults] = React.useState<any>(null);
  const [jobs, setJobs] = React.useState<Job[]>([]);
  const [selectedJob, setSelectedJob] = React.useState<Job | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);

  // New state for multi-step processing
  const [processingStatus, setProcessingStatus] = React.useState<
    ProcessingStatus[]
  >([]);
  const [currentFileUuid, setCurrentFileUuid] = React.useState<string | null>(
    null
  );
  const [pollingInterval, setPollingInterval] =
    React.useState<NodeJS.Timeout | null>(null);
  const [extractedData, setExtractedData] = React.useState<any>(null);
  const [recentCandidateData, setRecentCandidateData] =
    React.useState<any>(null);

  // Fetch active jobs
  const fetchCandidate = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/candidate");

      if (!response.ok) {
        throw new Error("Failed to fetch candidate");
      }

      const data = await response.json();

      console.log(data?.Items, "<<<<<<< Candidate Data >>>>>>");

      // Sort and get latest 4 by created_on
      const sortedData = data?.Items?.filter(
        (item: { created_on?: string }) => item.created_on
      ).sort(
        (a: { created_on: string }, b: { created_on: string }) =>
          new Date(b.created_on).getTime() - new Date(a.created_on).getTime()
      );

      const latestFour = sortedData.slice(0, 4);

      console.log(latestFour, "<<<<<<< Recent 4 Candidates >>>>>>");
      setRecentCandidateData(latestFour);
    } catch (error) {
      console.error("Error fetching candidate:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const fetchJobs = async () => {
      try {
        setIsLoading(true);
        const response = await fetch("/api/jobDescription?Status=Active");

        if (!response.ok) {
          throw new Error("Failed to fetch jobs");
        }

        const data = await response.json();
        const jobsData = Array.isArray(data) ? data : data.data || [];

        // Map API data to our Job interface
        const formattedJobs = jobsData.map((job: any) => ({
          id: job.jobdescid,
          title: job.jobtitle,
          description: job.responsibilities,
        }));

        setJobs(formattedJobs);
      } catch (error) {
        console.error("Error fetching jobs:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchJobs();
    fetchCandidate();
  }, []);

  // Cleanup polling interval on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [pollingInterval]);

  // Handle job selection
  const handleJobSelect = (job: Job) => {
    setSelectedJob(job);
  };

  // Helper function to format education data
  const formatEducation = (education: any): string => {
    if (!education) return "Not specified";

    // If it's already a string, return as is
    if (typeof education === "string") return education;

    // If it's an array of education objects
    if (Array.isArray(education)) {
      return education
        .map((edu) => {
          const parts = [];
          if (edu.degree) parts.push(edu.degree);
          if (edu.field_of_study) parts.push(`in ${edu.field_of_study}`);
          if (edu.institution) parts.push(`from ${edu.institution}`);
          if (edu.start_date && edu.end_date) {
            parts.push(`(${edu.start_date} - ${edu.end_date})`);
          } else if (edu.start_date) {
            parts.push(`(${edu.start_date})`);
          }
          return parts.join(" ");
        })
        .join("; ");
    }

    // If it's a single education object
    if (typeof education === "object") {
      const parts = [];
      if (education.degree) parts.push(education.degree);
      if (education.field_of_study)
        parts.push(`in ${education.field_of_study}`);
      if (education.institution) parts.push(`from ${education.institution}`);
      if (education.start_date && education.end_date) {
        parts.push(`(${education.start_date} - ${education.end_date})`);
      } else if (education.start_date) {
        parts.push(`(${education.start_date})`);
      }
      return parts.join(" ") || "Education details available";
    }

    return "Not specified";
  };

  // Helper function to format experience data
  const formatExperience = (experience: any): string => {
    if (!experience) return "Not specified";

    // If it's already a string, return as is
    if (typeof experience === "string") return experience;

    // If it's an array of experience objects
    if (Array.isArray(experience)) {
      return experience
        .map((exp) => {
          const parts = [];
          if (exp.title) parts.push(exp.title);
          if (exp.company) parts.push(`at ${exp.company}`);
          if (exp.start_date && exp.end_date) {
            parts.push(`(${exp.start_date} - ${exp.end_date})`);
          } else if (exp.start_date) {
            parts.push(`(${exp.start_date} - Present)`);
          }
          return parts.join(" ");
        })
        .join("; ");
    }

    // If it's a single experience object
    if (typeof experience === "object") {
      const parts = [];
      if (experience.title) parts.push(experience.title);
      if (experience.company) parts.push(`at ${experience.company}`);
      if (experience.start_date && experience.end_date) {
        parts.push(`(${experience.start_date} - ${experience.end_date})`);
      } else if (experience.start_date) {
        parts.push(`(${experience.start_date} - Present)`);
      }
      return parts.join(" ") || "Experience details available";
    }

    return "Not specified";
  };

  // Helper function to format skills data
  const formatSkills = (skills: any): string[] => {
    if (!skills) return [];

    // If it's already an array of strings, return as is
    if (
      Array.isArray(skills) &&
      skills.every((skill) => typeof skill === "string")
    ) {
      return skills;
    }

    // If it's an array of objects with skill names
    if (Array.isArray(skills)) {
      return skills.map((skill) => {
        if (typeof skill === "string") return skill;
        if (typeof skill === "object") {
          return (
            skill.name || skill.skill || skill.title || JSON.stringify(skill)
          );
        }
        return String(skill);
      });
    }

    // If it's a single skill object or string
    if (typeof skills === "string") return [skills];
    if (typeof skills === "object") {
      return [
        skills.name || skills.skill || skills.title || JSON.stringify(skills),
      ];
    }

    return [];
  };

  // Helper function to format recommendations/weaknesses data
  const formatTextArray = (items: any): string[] => {
    if (!items) return [];

    // If it's already an array of strings, return as is
    if (
      Array.isArray(items) &&
      items.every((item) => typeof item === "string")
    ) {
      return items;
    }

    // If it's an array of objects or mixed types
    if (Array.isArray(items)) {
      return items.map((item) => {
        if (typeof item === "string") return item;
        if (typeof item === "object") {
          return (
            item.text ||
            item.description ||
            item.content ||
            JSON.stringify(item)
          );
        }
        return String(item);
      });
    }

    // If it's a single item
    if (typeof items === "string") return [items];
    if (typeof items === "object") {
      return [
        items.text ||
          items.description ||
          items.content ||
          JSON.stringify(items),
      ];
    }

    return [];
  };

  // Helper function to safely extract string value from any data type
  const safeStringValue = (
    value: any,
    fallback: string = "Not specified"
  ): string => {
    if (!value) return fallback;
    if (typeof value === "string") return value;
    if (typeof value === "number") return String(value);
    if (typeof value === "object") {
      // Try common property names for text content
      const textProps = [
        "text",
        "value",
        "content",
        "description",
        "name",
        "title",
      ];
      for (const prop of textProps) {
        if (value[prop] && typeof value[prop] === "string") {
          return value[prop];
        }
      }
      // If no text property found, stringify the object
      return JSON.stringify(value);
    }
    return String(value);
  };

  // Fetch analysis history from API
  const fetchAnalysisHistory = async () => {
    try {
      // Get all active jobs first
      const jobsResponse = await fetch("/api/jobDescription?Status=Active", {
        signal: AbortSignal.timeout(10000), // 10 second timeout
      });
      if (!jobsResponse.ok) {
        throw new Error(`Failed to fetch jobs: ${jobsResponse.status}`);
      }

      // Check if response has content before parsing JSON
      const jobsResponseText = await jobsResponse.text();
      if (!jobsResponseText.trim()) {
        console.warn("Empty response from jobs API");
        return;
      }

      let jobsData;
      try {
        jobsData = JSON.parse(jobsResponseText);
      } catch (parseError) {
        console.error("Failed to parse jobs response:", parseError);
        return;
      }

      const activeJobs = Array.isArray(jobsData)
        ? jobsData
        : jobsData.data || [];

      // Create a map of job IDs to job titles for quick lookup
      const jobTitleMap = new Map();
      activeJobs.forEach((job: any) => {
        jobTitleMap.set(job.jobdescid, job.jobtitle);
      });

      // Fetch candidates for all jobs with completed analysis
      const allCandidates: AnalysisHistoryItem[] = [];

      for (const job of activeJobs) {
        try {
          // Fetch candidates for this job with status 'initial' (completed analysis)
          let candidatesResponse;
          try {
            candidatesResponse = await fetch(
              `/api/extractedData?Job_id=${job.jobdescid}&status=initial`,
              {
                signal: AbortSignal.timeout(10000), // 10 second timeout
              }
            );
          } catch (fetchError) {
            console.error(
              `Network error fetching candidates for job ${job.jobdescid}:`,
              fetchError
            );
            continue;
          }

          if (candidatesResponse.ok) {
            // Check if response has content before parsing JSON
            const candidatesResponseText = await candidatesResponse.text();
            if (!candidatesResponseText.trim()) {
              console.warn(
                `Empty response for candidates of job ${job.jobdescid}`
              );
              continue;
            }

            let candidatesData;
            try {
              candidatesData = JSON.parse(candidatesResponseText);
            } catch (parseError) {
              console.error(
                `Failed to parse candidates response for job ${job.jobdescid}:`,
                parseError
              );
              continue;
            }

            if (Array.isArray(candidatesData)) {
              // Process each candidate
              for (const candidate of candidatesData) {
                try {
                  // Fetch candidate details to get resume percentage and other info
                  let candidateDetailsResponse;
                  try {
                    candidateDetailsResponse = await fetch(
                      `/api/candidate?candidateid=${candidate.candidateid}`,
                      {
                        signal: AbortSignal.timeout(10000), // 10 second timeout
                      }
                    );
                  } catch (fetchError) {
                    console.error(
                      `Network error fetching candidate details for ${candidate.candidateid}:`,
                      fetchError
                    );
                    continue;
                  }

                  if (candidateDetailsResponse.ok) {
                    // Check if response has content before parsing JSON
                    const candidateDetailsText =
                      await candidateDetailsResponse.text();
                    if (!candidateDetailsText.trim()) {
                      console.warn(
                        `Empty response for candidate details ${candidate.candidateid}`
                      );
                      continue;
                    }

                    let candidateDetails;
                    try {
                      candidateDetails = JSON.parse(candidateDetailsText);
                    } catch (parseError) {
                      console.error(
                        `Failed to parse candidate details for ${candidate.candidateid}:`,
                        parseError
                      );
                      continue;
                    }

                    // Fetch extracted data for skills
                    let extractedDataResponse;
                    try {
                      extractedDataResponse = await fetch(
                        `/api/extractedData?id=${candidate.fileid}`,
                        {
                          signal: AbortSignal.timeout(10000), // 10 second timeout
                        }
                      );
                    } catch (fetchError) {
                      console.error(
                        `Network error fetching extracted data for ${candidate.fileid}:`,
                        fetchError
                      );
                      extractedDataResponse = null;
                    }

                    let skills: string[] = [];

                    if (extractedDataResponse && extractedDataResponse.ok) {
                      // Check if response has content before parsing JSON
                      const extractedDataText =
                        await extractedDataResponse.text();
                      if (extractedDataText.trim()) {
                        try {
                          const extractedData = JSON.parse(extractedDataText);
                          if (extractedData.processedJson?.skills) {
                            skills = formatSkills(
                              extractedData.processedJson.skills
                            );
                          }
                        } catch (parseError) {
                          console.error(
                            `Failed to parse extracted data for ${candidate.fileid}:`,
                            parseError
                          );
                        }
                      }
                    }

                    // Format the analyzed date
                    const rawDate =
                      candidateDetails.createdAt ||
                      candidate.createdAt ||
                      new Date().toISOString();
                    const analyzedDate = new Date(rawDate).toLocaleDateString(
                      "en-US",
                      {
                        year: "numeric",
                        month: "short",
                        day: "numeric",
                      }
                    );

                    // Create analysis history item
                    const historyItem: AnalysisHistoryItem = {
                      id: candidate.candidateid || candidate.fileid,
                      fileName: candidate.fileid || "Unknown File",
                      candidateName:
                        candidateDetails.name ||
                        candidateDetails.email ||
                        "Unknown Candidate",
                      score: candidateDetails.Resume_Percentage || 0,
                      matchedSkills: skills.slice(0, 5), // Limit to top 5 skills
                      analyzedDate: analyzedDate,
                      analyzedDateRaw: rawDate,
                      status: "completed",
                      jobTitle: jobTitleMap.get(job.jobdescid) || "Unknown Job",
                    };

                    allCandidates.push(historyItem);
                  }
                } catch (error) {
                  console.error(
                    `Error fetching details for candidate ${candidate.candidateid}:`,
                    error
                  );
                  // Add a fallback entry even if details fail
                  const fallbackDate =
                    candidate.createdAt || new Date().toISOString();
                  const fallbackAnalyzedDate = new Date(
                    fallbackDate
                  ).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                  });

                  const fallbackItem: AnalysisHistoryItem = {
                    id: candidate.candidateid || candidate.fileid,
                    fileName: candidate.fileid || "Unknown File",
                    candidateName: "Unknown Candidate",
                    score: 0,
                    matchedSkills: [],
                    analyzedDate: fallbackAnalyzedDate,
                    analyzedDateRaw: fallbackDate,
                    status: "completed",
                    jobTitle: jobTitleMap.get(job.jobdescid) || "Unknown Job",
                  };
                  allCandidates.push(fallbackItem);
                }
              }
            }
          }
        } catch (error) {
          console.error(
            `Error fetching candidates for job ${job.jobdescid}:`,
            error
          );
        }
      }

      // Sort by analyzed date (newest first) and set the history
      allCandidates.sort((a, b) => {
        const dateA = new Date(a.analyzedDateRaw).getTime();
        const dateB = new Date(b.analyzedDateRaw).getTime();
        return dateB - dateA; // Newest first
      });
      return allCandidates;
    } catch (error) {
      console.error("Error fetching analysis history:", error);

      // Handle specific error types
      if (error instanceof Error) {
        if (error.name === "AbortError") {
          console.error("Request timed out");
        } else if (
          error.name === "TypeError" &&
          error.message.includes("fetch")
        ) {
          console.error("Network error occurred");
        }
      }

      // Set empty array on error to show empty state
      return [];
    } finally {
    }
  };

  const tableColumns = [
    {
      key: "name",
      title: "Candidate",
      sortable: true,
      render: (value: string, row: any) => {
        const getInitials = (name: string) => {
          if (!name) return "";
          const parts = name.split(" ");
          if (parts.length === 1) return parts[0][0]?.toUpperCase() || "";
          return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
        };
        const initials = getInitials(value);
        const bgColor = "#2563eb";
        return (
          <div className="flex items-center gap-3">
            <span
              className="inline-flex items-center justify-center w-9 h-9 rounded-full text-lg font-bold text-white shadow"
              style={{ background: bgColor }}
            >
              {initials}
            </span>
            <span className="font-medium text-gray-900">{value}</span>
          </div>
        );
      },
    },
    {
      key: "Resume_Percentage",
      title: "Match Score",
      sortable: true,
      render: (value: number) => (
        <div className="flex items-center">
          <span
            className={`font-semibold ${
              value >= 70
                ? "text-green-600"
                : value >= 55
                ? "text-yellow-600"
                : "text-red-600"
            }`}
          >
            {value}%
          </span>
        </div>
      ),
    },
    {
      key: "key_skills",
      title: "Top Skills",
      render: (skills: string[]) => (
        <div className="flex flex-wrap gap-1">
          {skills?.slice(0, 3).map((skill, index) => (
            <Badge key={index} variant="info" size="sm">
              {skill}
            </Badge>
          ))}
          {skills?.length > 3 && (
            <Badge variant="default" size="sm">
              +{skills.length - 3}
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: "status",
      title: "Status",
      sortable: true,
      render: (value: string) => {
        const getVariant = (status: string) => {
          switch (status.toLowerCase()) {
            case "hired":
              return "success";
            case "rejected":
              return "danger";
            case "interview":
            case "ai interview":
            case "technical round":
            case "manager round":
            case "hr round":
              return "info";
            default:
              return "default";
          }
        };

        return (
          <Badge variant={getVariant(value)} size="sm">
            {value.replace(/\b\w/g, (l) => l.toUpperCase())}
          </Badge>
        );
      },
    },

    {
      key: "created_on",
      title: "Analyzed Date",
      sortable: true,
    },
  ];

  console.log(selectedJob, "<<<<<<< selectedJob >>>>>>");

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  // console.log(selectedJob, "<<<<<<< selectedJob >>>>>>");

  // Step 1: Initial File Upload with Job ID
  const uploadFile = async (file: File) => {
    console.log(selectedJob, "<<<<<<< selectedJob >>>>>>");

    if (!selectedJob) {
      alert("Please select a job first");
      return;
    }

    setIsAnalyzing(true);
    setProcessingStatus([
      { step: "upload", message: "Uploading resume...", completed: false },
    ]);

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("jobId", selectedJob.id);
      formData.append("JobDesc", selectedJob.description);

      const response = await fetch("/api/candidateResume/multi", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Failed to upload resume");
      }

      const result = await response.json();
      const fileUuid = result[0].fileuuid;

      console.log(result[0], "result>>>>");

      if (!fileUuid) {
        throw new Error("No file UUID received from upload");
      }

      setCurrentFileUuid(fileUuid);

      // Update status to show upload completed
      setProcessingStatus([
        {
          step: "upload",
          message: "File Uploaded Successfully",
          completed: true,
        },
        { step: "extract", message: "Resume Extracted", completed: false },
        { step: "ai", message: "LLM Processing", completed: false },
      ]);

      // Start polling for status
      startStatusPolling(fileUuid);
    } catch (error) {
      console.error("Error uploading file:", error);
      setProcessingStatus([
        {
          step: "failed",
          message: "Upload failed. Please try again.",
          completed: false,
        },
      ]);
      setIsAnalyzing(false);
    }
  };

  // Step 2: Status Polling Implementation
  const startStatusPolling = (fileUuid: string) => {
    const interval = setInterval(async () => {
      try {
        const response = await fetch(`/api/resumeStatus?id=${fileUuid}`);

        if (!response.ok) {
          throw new Error("Failed to check status");
        }

        const statusData = await response.json();
        const status = statusData.status;

        // Update processing status based on current stage
        if (status === "File Uploaded Successfully") {
          setProcessingStatus([
            {
              step: "upload",
              message: "File Uploaded Successfully",
              completed: true,
            },
            { step: "extract", message: "Resume Extracted", completed: false },
            { step: "ai", message: "LLM Processing", completed: false },
          ]);
        } else if (status === "PDF Extracted") {
          setProcessingStatus([
            {
              step: "upload",
              message: "File Uploaded Successfully",
              completed: true,
            },
            { step: "extract", message: "PDF Extracted", completed: true },
            { step: "ai", message: "LLM Processing", completed: false },
          ]);
        } else if (status === "File sent to AI") {
          setProcessingStatus([
            {
              step: "upload",
              message: "File Uploaded Successfully",
              completed: true,
            },
            { step: "extract", message: "PDF Extracted", completed: true },
            { step: "ai", message: "File sent to AI", completed: true },
            {
              step: "complete",
              message: "Completed Process",
              completed: false,
            },
          ]);

          // Stop polling and extract data
          clearInterval(interval);
          setPollingInterval(null);
          await extractProcessedData(fileUuid);
        } else if (status === "failed") {
          setProcessingStatus([
            {
              step: "failed",
              message: "Processing failed. Please try again.",
              completed: false,
            },
          ]);
          clearInterval(interval);
          setPollingInterval(null);
          setIsAnalyzing(false);
        }
      } catch (error) {
        console.error("Error polling status:", error);
        setProcessingStatus([
          {
            step: "failed",
            message: "Status check failed. Please try again.",
            completed: false,
          },
        ]);
        clearInterval(interval);
        setPollingInterval(null);
        setIsAnalyzing(false);
      }
    }, 3000); // Poll every 3 seconds

    setPollingInterval(interval);
  };

  // Step 3: Extract and Display Results
  const extractProcessedData = async (fileUuid: string) => {
    try {
      // Validate selectedJob early
      if (!selectedJob?.id) {
        throw new Error("No job selected");
      }

      // Parallelize all independent API calls
      const [dataResponse, candidateIdResponse] = await Promise.all([
        fetch(`/api/extractedData?id=${fileUuid}`),
        fetch(`/api/extractedData?Job_id=${selectedJob.id}&status=initial`),
      ]);

      // Early error handling for failed responses
      if (!dataResponse.ok) throw new Error("Failed to extract processed data");
      if (!candidateIdResponse.ok)
        throw new Error("Failed to extract candidate ID");

      // Process responses in parallel with better error handling
      const [data, candidateIdData] = await Promise.all([
        dataResponse.json().catch(() => ({})),
        candidateIdResponse.json().catch(() => []),
      ]);

      // Validate data structure
      if (!data?.processedJson) {
        console.error("No processed data found for file:", fileUuid);
        throw new Error(
          "Resume processing not completed yet. Please try again later."
        );
      }

      if (!Array.isArray(candidateIdData)) {
        console.error(
          "Invalid candidate data format received:",
          candidateIdData
        );
        throw new Error("Server returned invalid candidate data format");
      }

      // More robust candidate matching
      const matchingCandidate = candidateIdData.find((candidate: any) => {
        if (!candidate?.fileid) return false;

        // Normalize both IDs for comparison
        const normalizeId = (id: string) => String(id).trim().toLowerCase();

        try {
          return normalizeId(candidate.fileid) === normalizeId(fileUuid);
        } catch (error) {
          console.error("Error comparing IDs:", error);
          return false;
        }
      });

      // Handle missing candidate case
      if (!matchingCandidate) {
        // Check if this is a new file that hasn't been processed yet
        const MAX_RETRY_ATTEMPTS = 3;
        const RETRY_DELAY_MS = 2000;

        for (let attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
          console.log(
            `Retrying candidate match (attempt ${attempt}/${MAX_RETRY_ATTEMPTS})...`
          );
          await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY_MS));

          // Refetch candidate data
          const retryResponse = await fetch(
            `/api/extractedData?Job_id=${selectedJob?.id}&status=initial`
          );
          const retryData = await retryResponse.json();

          const retryCandidate = retryData.find(
            (c: any) =>
              String(c?.fileid).trim().toLowerCase() ===
              String(fileUuid).trim().toLowerCase()
          );

          if (retryCandidate) {
            console.log("Found candidate on retry attempt:", attempt);
            return await extractProcessedData(fileUuid); // Recursively retry with found candidate
          }
        }

        // Final error if still not found
        throw new Error(
          `Your resume has been processed but isn't yet associated with a candidate.\n` +
            `File UUID: ${fileUuid}\n` +
            `Please contact support if this persists.`
        );
      }

      // Fetch candidate details
      const candidateResponse = await fetch(
        `/api/candidate?candidateid=${matchingCandidate.candidateid}`
      );
      if (!candidateResponse.ok)
        throw new Error("Failed to fetch candidate details");
      const candidate = await candidateResponse.json();

      // Set extracted data early
      const { processedJson } = data;
      setExtractedData(processedJson);

      // Prepare analysis results object
      const analysisData = {
        score: candidate?.Resume_Percentage || 0,
        skills: formatSkills(processedJson.skills),
        experience: formatExperience(
          processedJson.years_of_experience.toString()
        ),
        education: formatEducation(processedJson.education),
        name: safeStringValue(processedJson.name),
        email: safeStringValue(processedJson.email),
        phone: safeStringValue(processedJson.phone_number),
        skill_set: safeStringValue(processedJson.key_skills.join(",")),
        recommendations: formatTextArray(processedJson.recommendations),
        weaknesses: formatTextArray(processedJson.weaknesses),
        jobTitle: selectedJob.title || "Selected Position",
      };

      setAnalysisResults(analysisData);

      // Prepare success status
      const successStatus: ProcessingStatus[] = [
        {
          step: "upload",
          message: "File Uploaded Successfully",
          completed: true,
        },
        { step: "extract", message: "PDF Extracted", completed: true },
        { step: "ai", message: "File sent to AI", completed: true },
        { step: "complete", message: "Completed Process", completed: true },
      ];

      // Handle high-score candidate workflow
      if (candidate?.Resume_Percentage > 50 && candidate?.candidateid) {
       
 
        const baseUrl =
          process.env.NEXT_PUBLIC_INTERVIEW_SCHEDULING_LINK?.trim() ||
          "https://dev.interview.neartekpod.io/schedule";

        // Send email notification to candidate
        const notificationData = {
          candidateId: candidate.candidateid,
          firstName: candidate.name,
          email: candidate.email,
          jobTitle: selectedJob?.title,
          companyName: "NearTekPod",
          linkexpiry: new Date(
            new Date().getTime() + 2 * 24 * 60 * 60 * 1000
          ).toISOString(), // You can make this dynamic
          scheduleformLink: candidate?.candidateid
            ? `${baseUrl}/${candidate.candidateid}`
            : `${baseUrl}/`, 
        };

        const response = await axios.post("/api/email", notificationData, {
          headers: {
            "Content-Type": "application/json",
          },
        });

        console.log(response, "response");

        if (!response) throw new Error("Failed to send email");

        if (response) {
          const today = new Date();
          const expiryDate = new Date(
            today.getTime() + 2 * 24 * 60 * 60 * 1000
          ); // +2 days

          const interviewScheduleLink = {
            candidateid: candidate.candidateid,
            link_sent_at: today.toISOString().split("T")[0], // e.g., "2025-07-21"
            link_expiry_at: expiryDate.toISOString().split("T")[0], // e.g., "2025-07-23"
          };

          const interviewResponses = await fetch("/api/candidate", {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(interviewScheduleLink),
          });
          if (!interviewResponses.ok)
            throw new Error("Failed to update candidate status");
        }
      }

      setProcessingStatus(successStatus);
      fetchCandidate();
    } catch (error) {
      console.error("Error extracting data:", error);
      setProcessingStatus([
        {
          step: "failed",
          message: "Data extraction failed. Please try again.",
          completed: false,
        },
      ]);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Handle analyze button click - now calls uploadFile
  const handleAnalyze = async () => {
    if (!selectedFile || !selectedJob) {
      alert("Please select both a job and a resume file");
      return;
    }

    // Clear previous results and status
    setAnalysisResults(null);
    setExtractedData(null);
    setProcessingStatus([]);

    // Clear any existing polling interval
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }

    await uploadFile(selectedFile);
  };

  const handleBulkAnalysis = () => {
    console.log("Start bulk analysis");
  };

  return (
    <div className="space-y-8 max-w-8xl mx-auto pb-12">
      {/* Header with Job Selection */}
      <div className=" rounded-2xl shadow-lg border border-gray-200 p-8 mb-8 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
        <div>
          <h1 className="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-700 to-purple-600 mb-2">
            Resume Analysis
          </h1>
          <p className="text-lg text-gray-700 font-medium">
            AI-powered resume screening and analysis
          </p>
        </div>
        <div className="flex gap-4">
          <Dropdown
            trigger={
              <Button
                variant="outline"
                className="min-w-[200px] justify-between text-base font-semibold border-2 border-blue-200 shadow-sm hover:border-blue-400"
              >
                <span>{selectedJob ? selectedJob.title : "Select Job"}</span>
                <Icon name="chevron-down" size="sm" />
              </Button>
            }
            items={jobs.map((job) => ({
              label: job.title,
              onClick: () => handleJobSelect(job),
            }))}
            loading={isLoading}
          />
          <Button
            variant="outline"
            onClick={handleBulkAnalysis}
            className="text-base font-semibold border-2 border-blue-200 shadow-sm hover:border-blue-400"
          >
            <Icon name="upload" size="sm" className="mr-2" />
            Bulk Analysis
          </Button>
        </div>
      </div>

      {/* Upload Section - More Compact */}
      <Card title="Upload Resume for Analysis" padding="md">
        <div className="space-y-3">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 transition-colors">
            {/* <Icon name="upload" size="md" className="mx-auto text-gray-400 mb-2" /> */}
            <div className="space-y-1">
              <p className="font-medium text-gray-900">
                Drop resume or click to browse
              </p>
              <p className="text-xs text-gray-500">PDF, DOC, DOCX (max 10MB)</p>
            </div>
            <input
              type="file"
              accept=".pdf,.doc,.docx"
              onChange={handleFileSelect}
              className="hidden"
              id="resume-upload"
            />
            <label
              htmlFor="resume-upload"
              className="mt-4 inline-flex items-center px-8 py-3 border border-blue-200 rounded-lg shadow-sm text-base font-semibold text-blue-700 bg-white hover:bg-blue-50 cursor-pointer transition-colors"
            >
              <Icon name="file" size="sm" className="mr-2" />
              Choose File
            </label>
          </div>

          {selectedFile && (
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Icon name="file" size="sm" className="text-white" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900 text-lg">
                    {selectedFile.name}
                  </p>
                  <p className="text-sm text-gray-500">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
              <Button
                variant="primary"
                onClick={handleAnalyze}
                disabled={isAnalyzing || !selectedJob}
                size="lg"
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-lg font-bold px-8 py-3"
              >
                {isAnalyzing ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Icon name="play" size="sm" className="mr-2" />
                    Analyze Resume
                  </>
                )}
              </Button>
            </div>
          )}
          {!selectedJob && selectedFile && (
            <p className="text-xs text-amber-600 font-semibold">
              Please select a job to analyze this resume against
            </p>
          )}
        </div>
      </Card>

      {/* Processing Status Indicator */}
      {processingStatus.length > 0 && (
        <Card
          title=""
          padding="none"
          className="rounded-2xl shadow-lg border border-gray-200"
        >
          <div className="p-8">
            <div className="flex flex-col md:flex-row md:items-center md:space-x-8 space-y-4 md:space-y-0">
              {processingStatus.map((status, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-3 flex-1 min-w-[180px]"
                >
                  <div
                    className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center text-xl font-bold border-2 ${
                      status.completed
                        ? "bg-green-100 text-green-600 border-green-300"
                        : status.step === "failed"
                        ? "bg-red-100 text-red-600 border-red-300"
                        : "bg-blue-100 text-blue-600 border-blue-300"
                    }`}
                  >
                    {status.completed ? (
                      <Icon name="check" size="md" />
                    ) : status.step === "failed" ? (
                      <Icon name="x" size="md" />
                    ) : (
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                    )}
                  </div>
                  <div className="flex-1">
                    <p
                      className={`text-base font-semibold ${
                        status.completed
                          ? "text-green-700"
                          : status.step === "failed"
                          ? "text-red-700"
                          : "text-blue-700"
                      }`}
                    >
                      Step {index + 1}: {status.message}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      )}


      {/* Analysis Results */}
      {analysisResults && (
        <Card
          title=""
          padding="none"
          className="rounded-2xl shadow-lg border border-gray-200"
        >
          <div className="p-8 space-y-8">
            {/* Score */}
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-28 h-28 bg-green-100 rounded-full mb-4 shadow-inner">
                <span className="text-4xl font-extrabold text-green-600">
                  {analysisResults.score}%
                </span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900">Match Score</h3>
              <p className="text-gray-600 text-lg">
                Match for {analysisResults.jobTitle}
              </p>
            </div>

            {/* Candidate Information */}
            {(analysisResults.name ||
              analysisResults.email ||
              analysisResults.phone ||
              analysisResults.experience) && (
              <div>
                <h4 className="font-semibold text-gray-900 mb-3 text-lg">
                  Candidate Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {analysisResults.name && (
                    <div className="flex items-center space-x-2">
                      <Icon name="user" size="md" className="text-blue-500" />
                      <span className="text-gray-700 text-base">
                        {String(analysisResults.name)}
                      </span>
                    </div>
                  )}
                  {analysisResults.email && (
                    <div className="flex items-center space-x-2">
                      <Icon name="mail" size="md" className="text-blue-500" />
                      <span className="text-gray-700 text-base">
                        {String(analysisResults.email)}
                      </span>
                    </div>
                  )}
                  {analysisResults.phone && (
                    <div className="flex items-center space-x-2">
                      <Icon name="phone" size="md" className="text-blue-500" />
                      <span className="text-gray-700 text-base">
                        {String(analysisResults.phone)}
                      </span>
                    </div>
                  )}
                  {analysisResults.experience && (
                    <div className="flex items-center space-x-2">
                      <Icon
                        name="map-pin"
                        size="md"
                        className="text-blue-500"
                      />
                      <span className="font-semibold text-gray-900 text-md">
                        Experience :{" "}
                      </span>
                      <span className="text-gray-700 text-base">
                        {String(analysisResults.experience)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Experience and Education */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {analysisResults.skill_set && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2 text-lg">
                    Skills
                  </h4>
                  <p className="text-gray-700 text-base">
                    {Array.isArray(analysisResults.skill_set)
                      ? analysisResults.skill_set.join(", ")
                      : analysisResults.skill_set
                          ?.replace(/[\[\]"]/g, "")
                          .split(",")
                          .map((s: string) => s.trim())
                          .join(", ")}
                  </p>
                </div>
              )}
              {analysisResults.education && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2 text-lg">
                    Education
                  </h4>
                  <p className="text-gray-700 text-base">
                    {String(analysisResults.education)}
                  </p>
                </div>
              )}
            </div>

            {/* Skills */}
            {analysisResults.skills && analysisResults.skills.length > 0 && (
              <div>
                <h4 className="font-semibold text-gray-900 mb-3 text-lg">
                  Identified Skills
                </h4>
                <div className="flex flex-wrap gap-2">
                  {analysisResults.skills.map(
                    (skill: string, index: number) => (
                      <Badge
                        key={index}
                        variant="info"
                        className="text-base px-3 py-1"
                      >
                        {String(skill)}
                      </Badge>
                    )
                  )}
                </div>
              </div>
            )}

            {/* Recommendations */}
            {analysisResults.recommendations &&
              analysisResults.recommendations.length > 0 && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3 text-lg">
                    Recommendations
                  </h4>
                  <ul className="space-y-2">
                    {analysisResults.recommendations.map(
                      (rec: string, index: number) => (
                        <li key={index} className="flex items-start space-x-2">
                          <Icon
                            name="check"
                            size="md"
                            className="text-green-500 mt-0.5"
                          />
                          <span className="text-gray-700 text-base">
                            {String(rec)}
                          </span>
                        </li>
                      )
                    )}
                  </ul>
                </div>
              )}

            {/* Weaknesses */}
            {analysisResults.weaknesses &&
              analysisResults.weaknesses.length > 0 && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3 text-lg">
                    Areas for Improvement
                  </h4>
                  <ul className="space-y-2">
                    {analysisResults.weaknesses.map(
                      (weakness: string, index: number) => (
                        <li key={index} className="flex items-start space-x-2">
                          <Icon
                            name="alert-circle"
                            size="md"
                            className="text-amber-500 mt-0.5"
                          />
                          <span className="text-gray-700 text-base">
                            {String(weakness)}
                          </span>
                        </li>
                      )
                    )}
                  </ul>
                </div>
              )}
          </div>
        </Card>
      )}

      {/* Analysis History */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Analysis History
        </h2>
        <DataTable
          loading={isLoading}
          columns={tableColumns}
          data={recentCandidateData || []}
          onSort={(key, direction) => console.log("Sort:", key, direction)}
          onRowClick={(row) => console.log("Row clicked:", row)}
        />
      </div>
    </div>
  );
}
