// lib/msalClient.js
import { ConfidentialClientApplication } from '@azure/msal-node';

const clientId = process.env.CLIENT_ID;
const tenantId = process.env.TENANT_ID;
const clientSecret = process.env.CLIENT_SECRET;

if (!clientId || !tenantId || !clientSecret) {
  throw new Error('Missing required environment variables for MSAL configuration.');
}

const msalConfig = {
  auth: {
    clientId,
    authority: `https://login.microsoftonline.com/${tenantId}`,
    clientSecret,
  },
};

const cca = new ConfidentialClientApplication(msalConfig);

export default cca;
