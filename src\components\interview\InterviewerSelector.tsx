'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Icon } from '@/components/ui';

interface Interviewer {
  id: string;
  name: string;
  email?: string;
  role?: string;
  department?: string;
  avatar?: string;
}

interface InterviewerSelectorProps {
  selectedInterviewer: string;
  onInterviewerChange: (interviewerId: string, interviewerName: string) => void;
  level: 'level1' | 'level2' | 'level3' | 'level4';
  disabled?: boolean;
  error?: string;
  className?: string;
}

const InterviewerSelector: React.FC<InterviewerSelectorProps> = ({
  selectedInterviewer,
  onInterviewerChange,
  level,
  disabled = false,
  error,
  className,
}) => {
  const [interviewers, setInterviewers] = useState<Interviewer[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Role mapping for different interview levels
  const getRoleForLevel = (level: string) => {
    switch (level) {
      case 'level2':
        return 'Technical Panel';
      case 'level3':
        return 'Manager';
      case 'level4':
        return 'HR';
      default:
        return 'Technical Panel';
    }
  };

  // Fetch interviewers based on level
  useEffect(() => {
    const fetchInterviewers = async () => {
      if (level === 'level1') {
        // AI Interview - no interviewer selection needed
        setInterviewers([]);
        return;
      }

      setLoading(true);
      try {
        const response = await fetch('/api/auth/user-by-role');
        const data = await response.json();
        
        if (data.data) {
          // Transform the data to match our interface
          const transformedInterviewers = data.data.map((user: any, index: number) => ({
            id: user.id || `interviewer-${index}`,
            name: user.name,
            email: user.email || `${user.name.toLowerCase().replace(/\s+/g, '.')}@company.com`,
            role: getRoleForLevel(level),
            department: level === 'level2' ? 'Engineering' : level === 'level3' ? 'Management' : 'HR',
          }));
          
          setInterviewers(transformedInterviewers);
        }
      } catch (error) {
        console.error('Failed to fetch interviewers:', error);
        setInterviewers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchInterviewers();
  }, [level]);

  // Filter interviewers based on search term
  const filteredInterviewers = interviewers.filter(interviewer =>
    interviewer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (interviewer.email && interviewer.email.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Get selected interviewer details
  const selectedInterviewerData = interviewers.find(i => i.id === selectedInterviewer);

  const handleInterviewerSelect = (interviewer: Interviewer) => {
    onInterviewerChange(interviewer.id, interviewer.name);
    setIsDropdownOpen(false);
    setSearchTerm('');
  };

  if (level === 'level1') {
    return (
      <div className={cn('space-y-2', className)}>
        <label className="block text-sm font-medium text-gray-700">
          Interviewer
        </label>
        <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-600">
          <Icon name="robot" size="sm" className="inline mr-2" />
          AI Interview System (Automated)
        </div>
        <p className="text-xs text-gray-500">
          Level 1 interviews are conducted automatically by AI
        </p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      <label className="block text-sm font-medium text-gray-700">
        Select Interviewer ({getRoleForLevel(level)})
        <span className="text-red-500 ml-1">*</span>
      </label>
      
      <div className="relative">
        {/* Selected Interviewer Display / Dropdown Trigger */}
        <div
          onClick={() => !disabled && setIsDropdownOpen(!isDropdownOpen)}
          className={cn(
            'w-full px-3 py-2 border border-gray-300 rounded-lg cursor-pointer transition-colors',
            'focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            disabled && 'bg-gray-100 cursor-not-allowed',
            error && 'border-red-500 focus:ring-red-500 focus:border-red-500',
            isDropdownOpen && 'ring-2 ring-blue-500 border-blue-500'
          )}
        >
          {selectedInterviewerData ? (
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-blue-600">
                  {selectedInterviewerData.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </span>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">{selectedInterviewerData.name}</p>
                <p className="text-xs text-gray-500">{selectedInterviewerData.role}</p>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <span className="text-gray-500">Select an interviewer</span>
              <Icon name="chevronDown" size="sm" className="text-gray-400" />
            </div>
          )}
        </div>

        {/* Dropdown Menu */}
        {isDropdownOpen && !disabled && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-hidden">
            {/* Search Input */}
            <div className="p-3 border-b border-gray-200">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search interviewers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-3 py-2 pl-9 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <Icon name="search" size="sm" className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </div>
            </div>

            {/* Interviewer List */}
            <div className="max-h-48 overflow-y-auto">
              {loading ? (
                <div className="p-4 text-center">
                  <Icon name="loader" size="sm" className="animate-spin mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Loading interviewers...</p>
                </div>
              ) : filteredInterviewers.length === 0 ? (
                <div className="p-4 text-center">
                  <Icon name="users" size="sm" className="mx-auto mb-2 text-gray-400" />
                  <p className="text-sm text-gray-500">
                    {searchTerm ? 'No interviewers found' : 'No interviewers available'}
                  </p>
                </div>
              ) : (
                filteredInterviewers.map((interviewer) => (
                  <div
                    key={interviewer.id}
                    onClick={() => handleInterviewerSelect(interviewer)}
                    className={cn(
                      'p-3 cursor-pointer transition-colors hover:bg-gray-50',
                      selectedInterviewer === interviewer.id && 'bg-blue-50 border-r-2 border-blue-500'
                    )}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-600">
                          {interviewer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </span>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{interviewer.name}</p>
                        <p className="text-xs text-gray-500">{interviewer.role} • {interviewer.department}</p>
                        {interviewer.email && (
                          <p className="text-xs text-gray-400">{interviewer.email}</p>
                        )}
                      </div>
                      {selectedInterviewer === interviewer.id && (
                        <Icon name="check" size="sm" className="text-blue-600" />
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </div>

      {/* Helper Text */}
      <p className="text-xs text-gray-500">
        Showing {getRoleForLevel(level)} team members available for {level.toUpperCase()} interviews
      </p>

      {/* Error Message */}
      {error && (
        <div className="flex items-center space-x-2 text-red-600">
          <Icon name="alert" size="sm" />
          <span className="text-sm">{error}</span>
        </div>
      )}

      {/* Click outside to close dropdown */}
      {isDropdownOpen && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </div>
  );
};

export default InterviewerSelector;
