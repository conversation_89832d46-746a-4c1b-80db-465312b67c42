import cca from '@/lib/msalClient';
import axios from 'axios';
import { format, addMinutes } from 'date-fns';

// Replace with your actual email sending utility
async function sendEmailToCandidate({
  to,
  subject,
  body,
}: {
  to: string;
  subject: string;
  body: string;
}) {
  console.log(`Sending email to ${to} with subject "${subject}"`);
  return Promise.resolve(); // Dummy for now
}

export async function POST(req: Request) {
  const body = await req.json();
  const {
    candidate_id,
    candidate_name,
    candidate_email,
    interviewer_email,
    interviewer_name,
    level,
    scheduled_date,
    scheduled_time,
    timezone,
    job_title,
  } = body;

  try {
    const tokenResponse = await cca.acquireTokenByClientCredential({
      scopes: ['https://graph.microsoft.com/.default'],
    });

    if (!tokenResponse || !tokenResponse.accessToken) {
      throw new Error('Failed to acquire access token');
    }
    const accessToken = tokenResponse.accessToken;

    // 🕒 Convert to ISO datetime
    const startDateTime = new Date(`${scheduled_date}T${scheduled_time}:00`);
    const endDateTime = addMinutes(startDateTime, 30);

    // 📅 Create Teams Meeting
    const event = {
      subject: `${level} Interview - ${candidate_name}`,
      body: {
        contentType: 'HTML',
        content: `Interview with ${candidate_name} for ${job_title}.`,
      },
      start: {
        dateTime: format(startDateTime, "yyyy-MM-dd'T'HH:mm:ss"),
        timeZone: timezone,
      },
      end: {
        dateTime: format(endDateTime, "yyyy-MM-dd'T'HH:mm:ss"),
        timeZone: timezone,
      },
      location: {
        displayName: 'Online',
      },
      attendees: [
        {
          emailAddress: {
            address: candidate_email,
            name: candidate_name,
          },
          type: 'Required',
        },
      ],
      isOnlineMeeting: true,
      onlineMeetingProvider: 'teamsForBusiness',
    };

    interface GraphEventResponse {
      id: string;
      onlineMeeting?: {
        joinUrl?: string;
      };
      [key: string]: any;
    }

    const response = await axios.post<GraphEventResponse>(
      `https://graph.microsoft.com/v1.0/users/${interviewer_email}/events`,
      event,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    const meetingLink = response.data.onlineMeeting?.joinUrl;

    // ✉️ Email to candidate
    await sendEmailToCandidate({
      to: candidate_email,
      subject: `Interview Confirmation - ${job_title}`,
      body: `
        <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <p>Dear ${candidate_name},</p>

          <p>We are pleased to inform you that your interview for the <strong>${job_title}</strong> position at <strong>NearTekPod</strong> has been successfully scheduled.</p>

          <h3 style="color: #0056b3;">📅 Interview Details</h3>
          <ul style="padding-left: 20px;">
            <li><strong>Date:</strong> ${scheduled_date}</li>
            <li><strong>Time:</strong> ${scheduled_time} (${timezone})</li>
            <li><strong>Level:</strong> ${level}</li>
            <li><strong>Mode:</strong> Online via Microsoft Teams</li>
          </ul>

          <p>
            👉 <strong>Join Link:</strong><br/>
            <a href="${meetingLink}" style="color: #1a73e8;">${meetingLink}</a>
          </p>

          <p>Please ensure you're in a quiet environment with a stable internet connection. We recommend joining a few minutes early to test your setup.</p>

          <p>We look forward to meeting you!</p>

          <p>Best regards,<br/>The NearTekPod Recruitment Team</p>

          <hr style="margin-top: 30px;" />
          <p style="font-size: 12px; color: #777;">
            This is an automated message. If you have any questions, please contact us.
          </p>
        </div>
      `,
    });

    return new Response(
      JSON.stringify({
        message: 'Interview scheduled and email sent successfully',
        eventId: response.data.id,
        meetingLink,
      }),
      { status: 200 }
    );
  } catch (error) {
    if (typeof error === 'object' && error !== null && 'response' in error && 'message' in error) {
      // @ts-ignore
      console.error('Error scheduling interview:', error.response?.data || error.message);
    } else {
      console.error('Error scheduling interview:', error);
    }
    return new Response(JSON.stringify({ error: 'Failed to schedule interview' }), {
      status: 500,
    });
  }
}
