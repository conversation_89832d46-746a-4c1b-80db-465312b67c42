import { NextRequest, NextResponse } from "next/server";
import axios from "axios";
import { DynamoDBtable } from "@/lib/db_actions";

const InterviewTable = new DynamoDBtable("interview");

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const candidateId = searchParams.get('candidate_id');
    const level = searchParams.get('level');

    if (!candidateId) {
      return NextResponse.json(
        { error: "candidate_id is required" },
        { status: 400 }
      );
    }

    // Fetch external feedback data
    let externalFeedback = null;
    

    // Fetch internal interview data
  const internalResponse = await axios.get(
  `${process.env.NODE_RED_URL}/getAIInterviewList?candidate_id=${candidateId}`
);


    console.log(internalResponse,"internalResponse>>>>>")

    const internalData: any[] = Array.isArray(internalResponse.data) ? internalResponse.data : [];

    console.log(internalData,">>>>>>>>internalData");

    // Filter by level if specified
    const filteredData = level
      ? internalData.filter((interview: any) => interview.level === level)
      : internalData;

    console.log(filteredData,">>>>>>>>filteredData");
    
    // Extract summary if present in the first element (adjust as needed)
    const summary = Array.isArray(internalData) && internalData.length > 0 && internalData[0].summary
      ? internalData[0].summary
      : null;

    // Combine internal and external data
    const combinedData = {
      candidate_id: candidateId,
      level,
      internal_interviews: filteredData,
      summary,
      last_updated: new Date().toISOString(),
    };

    return NextResponse.json(combinedData);
  } catch (error: unknown) {
    console.error("Interview summary fetch error:", error);

    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { status?: number; data?: any }; message?: string };
      return NextResponse.json(
        {
          message: "Failed to fetch interview summary",
          error: axiosError.message || "Unknown error",
        },
        { status: axiosError.response?.status || 500 }
      );
    }
    
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { candidate_id, level, status, decision, feedback_data } = body;

    // Validate required fields
    if (!candidate_id || !level || !status) {
      return NextResponse.json(
        { error: "candidate_id, level, and status are required" },
        { status: 400 }
      );
    }

    // Update internal interview status
    const updateData = {
      candidate_id,
      level,
      status,
      decision: decision || 'pending',
      updated_at: new Date().toISOString(),
      ...feedback_data,
    };

    // Update external system if needed
    try {
      await axios.put(
        `${process.env.NODE_RED_URL}/updateInterviewStatus`,
        updateData
      );
    } catch (updateError: unknown) {
      const errorMessage = updateError && typeof updateError === 'object' && 'message' in updateError
        ? (updateError as { message: string }).message
        : "Unknown update error";
      console.warn("Failed to update external system:", errorMessage);
      // Continue with internal update even if external fails
    }

    // Update internal database
    const response = await fetch('/api/interview', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      throw new Error('Failed to update internal interview data');
    }

    return NextResponse.json({
      success: true,
      message: "Interview status updated successfully",
      data: updateData,
    });
  } catch (error: unknown) {
    console.error("Interview status update error:", error);

    const errorMessage = error && typeof error === 'object' && 'message' in error
      ? (error as { message: string }).message
      : "Unknown error";

    return NextResponse.json(
      {
        success: false,
        error: "Failed to update interview status",
        details: errorMessage,
      },
      { status: 500 }
    );
  }
}
