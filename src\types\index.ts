// Core data types for the recruitment application

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'recruiter' | 'hiring_manager';
  avatar?: string;
}

export interface Job {
  id: string;
  title: string;
  department: string;
  location: string;
  type: 'full-time' | 'part-time' | 'contract' | 'internship';
  status: 'active' | 'paused' | 'closed' | 'draft';
  description: string;
  requirements: string[];
  postedDate: Date;
  closingDate?: Date;
  salary?: {
    min: number;
    max: number;
    currency: string;
  };
}

export interface Candidate {
  id: string;
  name: string;
  email: string;
  phone?: string;
  phone_number?: string; // Alternative property name used in API
  avatar?: string;
  resume?: string;
  status: 'new' | 'screening' | 'interview' | 'offer' | 'hired' | 'rejected';
  appliedJobs: string[];
  skills: string[];
  key_skills?: string[]; // Alternative property name used in API
  experience: number;
  years_of_experience?: string | number; // Alternative property name used in API
  location: string;
  appliedDate: Date;
  created_on?: string; // Creation timestamp from API
}

export interface DashboardMetric {
  id: string;
  title: string;
  value: number | string;
  change?: number;
  changeType?: 'increase' | 'decrease';
  icon?: string;
  color?: 'blue' | 'green' | 'orange' | 'red' | 'purple';
}

export interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  action: () => void;
  color?: 'blue' | 'green' | 'orange' | 'purple';
}

// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'warning';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export interface CardProps extends BaseComponentProps {
  title?: string;
  subtitle?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
}

export interface BadgeProps extends BaseComponentProps {
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info';
  size?: 'sm' | 'md' | 'lg';
}

export interface InputProps extends BaseComponentProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'search';
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  error?: string;
  label?: string;
  required?: boolean;
}

// Navigation and routing interfaces
export interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  href: string;
  badge?: string;
  description?: string;
}

export interface SidebarItem extends NavigationItem {
  active?: boolean;
}

export interface RouteInfo {
  path: string;
  title: string;
  description?: string;
}

export interface FilterOption {
  value: string;
  label: string;
  count?: number;
}

export interface TableColumn {
  key: string;
  title: string;
  sortable?: boolean;
  render?: (value: any, row: any) => React.ReactNode;
  width?: string;
}

// Form interfaces
export interface FormData {
  [key: string]: string | number | boolean | string[];
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'textarea' | 'select';
  placeholder?: string;
  required?: boolean;
  validation?: ValidationRule;
  options?: { value: string; label: string }[];
  helpText?: string;
}

// Interview Management Interfaces
export interface InterviewSchedule {
  id: string;
  candidate_id: string;
  interviewer_id: string;
  interviewer_name: string;
  level: 'level1' | 'level2' | 'level3' | 'level4';
  scheduled_date: string; // ISO date string
  scheduled_time: string; // HH:MM format
  timezone: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'rescheduled';
  created_at: string;
  updated_at: string;
  calendar_event_id?: string;
  email_sent: boolean;
  email_sent_at?: string;
  notes?: string;
}

export interface InterviewFeedback {
  id: string;
  candidate_id: string;
  interview_id: string;
  level: 'level1' | 'level2' | 'level3' | 'level4';
  interviewer_id: string;
  interviewer_name: string;
  decision: 'passed' | 'failed' | 'pending' | 'in_progress';
  overall_rating?: number; // 1-10 scale
  technical_rating?: number;
  communication_rating?: number;
  cultural_fit_rating?: number;
  feedback_summary: string;
  detailed_feedback: string;
  recommendations: string;
  strengths: string[];
  areas_for_improvement: string[];
  completed_at: string;
  created_at: string;
}

export interface InterviewLevel {
  id: number;
  name: string;
  description: string;
  type: 'automated' | 'manual';
  required_role?: string;
  estimated_duration?: number; // in minutes
}

export interface SchedulingFormData {
  interviewer_id: string;
  interviewer_name: string;
  scheduled_date: Date;
  scheduled_time: string;
  timezone: string;
  notes?: string;
}

export interface ExternalFeedbackResponse {
  candidate_id: string;
  level: string;
  status: 'completed' | 'in_progress' | 'pending';
  decision?: 'passed' | 'failed';
  feedback_data?: {
    overall_rating?: number;
    technical_rating?: number;
    communication_rating?: number;
    cultural_fit_rating?: number;
    feedback_summary?: string;
    detailed_feedback?: string;
    recommendations?: string;
    strengths?: string[];
    areas_for_improvement?: string[];
    completed_at?: string;
  };
  interviewer_info?: {
    id: string;
    name: string;
    email: string;
  };
}

// API Response interfaces
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Search and filter interfaces
export interface SearchFilters {
  query?: string;
  status?: string[];
  department?: string[];
  location?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
}
