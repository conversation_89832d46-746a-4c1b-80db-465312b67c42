import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(req: NextRequest) {

  try {
    const {
      candidateId,
      firstName,
      email,
      jobId,
      interviewLink,
      scheduledDate,
      timeSlot,
      interviewType,
    } = await req.json();

    const companyName = req.headers.get("X-Auth-User-Organization");

    const jobTitleResponse = await fetch(`${process.env.NODE_RED_URL}/getjob-descid?jobdescid=${jobId}`);
    const jobTitle = (await jobTitleResponse.json()).jobtitle;

    console.log(jobTitleResponse, "<<<<<<< jobTitleResponse >>>>>>");

    console.log(interviewLink, "<<<<<<< interviewLink >>>>>>");
    
    

    console.log(jobTitle, "<<<<<<< jobTitle >>>>>>");

    // Get access token for Microsoft Graph API
    const tokenResponse = await axios.post(
      `https://login.microsoftonline.com/${process.env.TENANT_ID}/oauth2/v2.0/token`,
      new URLSearchParams({
        client_id: process.env.CLIENT_ID!,
        client_secret: process.env.CLIENT_SECRET!,
        scope: 'https://graph.microsoft.com/.default',
        grant_type: 'client_credentials',
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    const accessToken = (tokenResponse.data as { access_token: string }).access_token;

    const emailBody = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">AI Interview Scheduled - ${interviewType}</h2>
        
        <p>Dear ${firstName},</p>
        
        <p>Great news! Your ${interviewType} for the <strong>${jobTitle}</strong> position at ${companyName} has been confirmed.</p>
        
        <div style="background-color: #f3f4f6; padding: 16px; border-radius: 8px; margin: 16px 0;">
          <h3 style="margin-top: 0;">Interview Details:</h3>
          <p><strong>Date:</strong> ${scheduledDate}</p>
          <p><strong>Time:</strong> ${timeSlot}</p>
          <p><strong>Type:</strong> ${interviewType}</p>
        </div>
        
        <div style="background-color: #dbeafe; padding: 16px; border-radius: 8px; margin: 16px 0;">
          <h3 style="margin-top: 0; color: #1d4ed8;">Ready to Start Your Interview?</h3>
          <p>Click the button below to begin your AI interview at the scheduled time:</p>
          <div style="text-align: center; margin: 20px 0;">
            <a href="${interviewLink}" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
              Start AI Interview
            </a>
          </div>
          <p style="font-size: 14px; color: #6b7280;">
            <strong>Important:</strong> Please ensure you have a stable internet connection and are in a quiet environment for the best interview experience.
          </p>
        </div>
        
        <p>If you have any questions or need to reschedule, please contact our HR team.</p>
        
        <p>Best regards,<br>
        ${companyName} Recruitment Team</p>
      </div>
    `;

    await axios.post(
      `https://graph.microsoft.com/v1.0/users/${process.env.SENDER_EMAIL}/sendMail`,
      {
        message: {
          subject: `AI Interview Link - ${jobTitle} at ${companyName}`,
          body: {
            contentType: 'HTML',
            content: emailBody,
          },
          toRecipients: [
            {
              emailAddress: {
                address: email,
              },
            },
          ],
        },
        saveToSentItems: 'true',
      },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return NextResponse.json({
      success: true,
      message: 'Interview link sent successfully',
    });
  } catch (error) {
    console.error('Error sending interview link email:', error);
    return NextResponse.json(
      { error: 'Failed to send interview link email' },
      { status: 500 }
    );
  }
}