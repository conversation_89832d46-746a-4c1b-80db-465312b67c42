'use client';

import React, { useState, useEffect, useMemo, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  <PERSON><PERSON>,
  Card,
  SearchBar,
  FilterDropdown,
  DataTable,
  Badge,
  JobPostingCard,
  Icon,
  ConfirmDialog
} from '@/components';
import { Job } from '@/types';
import { formatDateConsistent } from '@/lib/utils';
import { JOBS_STATUS } from '@/Enums';

// Interface for job data from API
interface ApiJobData {
  jobdescid: string;
  jobtitle: string;
  responsibilities: string;
  requirement: string;
  skills: string;
  location: string;
  status: string;
  publishToExternalChannels: boolean;
  created_by: string;
  organization?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Helper function to convert API data to Job interface
const convertApiJobToJob = (apiJob: ApiJobData): Job => {
  return {
    id: apiJob.jobdescid,
    title: apiJob.jobtitle,
    department: 'General', // Default since not in API
    location: apiJob.location,
    type: 'full-time', // Default since not in API
    status: apiJob.status.toLowerCase() as 'active' | 'paused' | 'closed' | 'draft',
    description: apiJob.responsibilities,
    requirements: apiJob.requirement.split(',').map(req => req.trim()),
    postedDate: apiJob.createdAt ? new Date(apiJob.createdAt) : new Date(),
    closingDate: undefined, // Not available in API
    salary: undefined, // Not available in API
  };
};

function JobsPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState('');
  const [tableSearchQuery, setTableSearchQuery] = useState('');
  const [statusFilters, setStatusFilters] = useState<string[]>([]);
  const [tableStatusFilter, setTableStatusFilter] = useState<string>('');
  const [showAllJobs, setShowAllJobs] = useState(false);

  const [locationFilters, setLocationFilters] = useState<string[]>([]);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    jobId: string | null;
    jobTitle: string;
  }>({
    isOpen: false,
    jobId: null,
    jobTitle: '',
  });

  // Fetch jobs from API
  const fetchJobs = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/jobDescription?Status=Active', {
        headers: {
          'X-Auth-User-Organization': 'demo-org',
          'X-Auth-User-Email': '<EMAIL>',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch jobs');
      }

      const data = await response.json();

      // Convert API data to Job interface
      const jobsData = Array.isArray(data) ? data : (data.data || []);
      const convertedJobs = jobsData.map((apiJob: ApiJobData) => convertApiJobToJob(apiJob));

      setJobs(convertedJobs);
    } catch (err) {
      console.error('Error fetching jobs:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  };

  // Load jobs on component mount and when refresh param is present
  useEffect(() => {
    fetchJobs();
  }, []);

  // Handle refresh parameter
  useEffect(() => {
    const refresh = searchParams.get('refresh');
    if (refresh === 'true') {
      fetchJobs();
      // Remove the refresh parameter from URL
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('refresh');
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, [searchParams]);

  // Filter jobs based on search query and filters
  const filteredJobs = useMemo(() => {
    return jobs.filter(job => {
      // Search query filter
      const searchMatch = !searchQuery || 
        job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        job.department.toLowerCase().includes(searchQuery.toLowerCase()) ||
        job.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
        job.description.toLowerCase().includes(searchQuery.toLowerCase());

      // Status filter
      const statusMatch = statusFilters.length === 0 || statusFilters.includes(job.status);

      // Location filter
      const locationMatch = locationFilters.length === 0 || locationFilters.includes(job.location.toLowerCase());

      return searchMatch && statusMatch && locationMatch;
    });
  }, [jobs, searchQuery, statusFilters, locationFilters]);

  // Filter table data based on table search query and status filter
  const filteredTableData = useMemo(() => {
    return filteredJobs.filter(job => {
      // Table search query filter
      const tableSearchMatch = !tableSearchQuery || 
        job.title.toLowerCase().includes(tableSearchQuery.toLowerCase()) ||
        job.department.toLowerCase().includes(tableSearchQuery.toLowerCase()) ||
        job.location.toLowerCase().includes(tableSearchQuery.toLowerCase()) ||
        job.description.toLowerCase().includes(tableSearchQuery.toLowerCase());

      // Table status filter
      const tableStatusMatch = !tableStatusFilter || job.status.toLowerCase() === tableStatusFilter.toLowerCase();

      return tableSearchMatch && tableStatusMatch;
    });
  }, [filteredJobs, tableSearchQuery, tableStatusFilter]);

  // Show delete confirmation dialog
  const showDeleteConfirmation = (job: Job) => {
    setDeleteDialog({
      isOpen: true,
      jobId: job.id,
      jobTitle: job.title,
    });
  };

  // Close delete confirmation dialog
  const closeDeleteConfirmation = () => {
    setDeleteDialog({
      isOpen: false,
      jobId: null,
      jobTitle: '',
    });
  };

  // Delete job function
  const handleDeleteJob = async () => {
    const { jobId } = deleteDialog;
    if (!jobId) return;

    try {
      setDeleteLoading(jobId);

      const response = await fetch(`/api/jobDescription?jobdescid=${jobId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete job');
      }

      // Remove job from local state
      setJobs(prevJobs => prevJobs.filter(job => job.id !== jobId));

      // Close dialog and show success message
      closeDeleteConfirmation();
      alert('Job deleted successfully!');
    } catch (err) {
      console.error('Error deleting job:', err);
      alert('Failed to delete job. Please try again.');
    } finally {
      setDeleteLoading(null);
    }
  };

  // View job details
  const handleViewJob = (job: Job) => {
    router.push(`/jobs/${job.id}`);
  };

  // Edit job
  const handleEditJob = (job: Job) => {
    router.push(`/jobs/${job.id}/edit`);
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSearchQuery('');
    setStatusFilters([]);
    setLocationFilters([]);
  };

  // Calculate dynamic filter options based on current jobs
  const statusOptions = [
    {
      value: 'active',
      label: 'Active',
      count: jobs.filter(job => job.status === 'active').length
    },
    {
      value: 'paused',
      label: 'Paused',
      count: jobs.filter(job => job.status === 'paused').length
    },
    {
      value: 'closed',
      label: 'Closed',
      count: jobs.filter(job => job.status === 'closed').length
    },
    {
      value: 'draft',
      label: 'Draft',
      count: jobs.filter(job => job.status === 'draft').length
    }
  ];

  const departmentOptions = [
    {
      value: 'engineering',
      label: 'Engineering',
      count: jobs.filter(job => job.department.toLowerCase() === 'engineering').length
    },
    {
      value: 'design',
      label: 'Design',
      count: jobs.filter(job => job.department.toLowerCase() === 'design').length
    },
    {
      value: 'general',
      label: 'General',
      count: jobs.filter(job => job.department.toLowerCase() === 'general').length
    }
  ];

  const locationOptions = [
    {
      value: 'chicago, illinois, us',
      label: 'Chicago, Illinois, US',
      count: jobs.filter(job => job.location.toLowerCase() === 'chicago, illinois, us').length
    },
    {
      value: 'trichy, tamil nadu, india',
      label: 'Trichy, Tamil Nadu, India',
      count: jobs.filter(job => job.location.toLowerCase() === 'trichy, tamil nadu, india').length
    }
  ];

  const tableColumns = [
    { key: 'title', title: 'Job Title', sortable: true },
    { key: 'department', title: 'Department', sortable: true },
    { key: 'location', title: 'Location', sortable: true },
    {
      key: 'status',
      title: 'Status',
      render: (value: string) => {
        const getStatusVariant = (status: string) => {
          switch (status.toLowerCase()) {
            case 'active': return 'success';
            case 'paused': return 'warning';
            case 'closed': return 'danger';
            case 'draft': return 'default';
            default: return 'default';
          }
        };
        return (
          <Badge variant={getStatusVariant(value)}>
            {value.charAt(0).toUpperCase() + value.slice(1)}
          </Badge>
        );
      }
    },
    { key: 'postedDate', title: 'Posted Date', sortable: true }
  ];

  const tableData = filteredTableData.map(job => ({
    ...job,
    postedDate: formatDateConsistent(job.postedDate)
  }));

  const handleCreateJob = () => {
    router.push('/jobs/create');
  };

  // Check if any filters are active
  const hasActiveFilters = searchQuery || statusFilters.length > 0 || locationFilters.length > 0;

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Job Postings</h1>
            <p className="text-gray-600">Manage and track your job openings</p>
          </div>
          <Button variant="primary" onClick={handleCreateJob} className="w-full sm:w-auto">
            Post New Job
          </Button>
        </div>

        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">Loading jobs...</span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Job Postings</h1>
            <p className="text-gray-600">Manage and track your job openings</p>
          </div>
          <Button variant="primary" onClick={handleCreateJob} className="w-full sm:w-auto">
            Post New Job
          </Button>
        </div>

        <Card padding="lg">
          <div className="text-center py-8">
            <Icon name="alert" size="xl" className="text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Jobs</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button variant="primary" onClick={fetchJobs}>
              Try Again
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Job Postings</h1>
          <p className="text-gray-600">
            Manage and track your job openings ({jobs.length} total)
          </p>
        </div>
        <Button variant="primary" onClick={handleCreateJob} className="w-full sm:w-auto">
          Post New Job
        </Button>
      </div>



             {/* Job Cards Grid */}
       <div>
         <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
           <h2 className="text-lg font-semibold text-gray-900">
             {hasActiveFilters ? 'Filtered' : 'Active'} Postings ({filteredJobs.length})
             {hasActiveFilters && (
               <span className="text-sm font-normal text-gray-500 ml-2">
                 (of {jobs.length} total)
               </span>
             )}
           </h2>
                       <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAllJobs(!showAllJobs)}
              className="mt-2 sm:mt-0"
            >
              {showAllJobs ? 'Show Less' : 'View All Jobs'}
            </Button>
         </div>
         {filteredJobs.length === 0 ? (
           <Card padding="lg">
             <div className="text-center py-8">
               <Icon name="plus" size="xl" className="text-gray-400 mx-auto mb-4" />
               <h3 className="text-lg font-medium text-gray-900 mb-2">
                 {hasActiveFilters ? 'No Jobs Found' : 'No Jobs Posted Yet'}
               </h3>
               <p className="text-gray-600 mb-4">
                 {hasActiveFilters 
                   ? 'No jobs match your current filters. Try adjusting your search criteria.'
                   : 'Get started by creating your first job posting to attract top talent.'
                 }
               </p>
               <div className="space-y-3">
                 <Button variant="primary" onClick={handleCreateJob}>
                   {hasActiveFilters ? 'Create New Job' : 'Post Your First Job'}
                 </Button>
                 {hasActiveFilters && (
                   <Button variant="outline" onClick={clearAllFilters}>
                     Clear All Filters
                   </Button>
                 )}
               </div>
             </div>
           </Card>
         ) : (
                       <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredJobs.slice(0, showAllJobs ? filteredJobs.length : 2).map((job) => (
                <div key={job.id} className="relative">
                  <JobPostingCard
                    job={job}
                    onView={() => handleViewJob(job)}
                    onEdit={() => handleEditJob(job)}
                    onDelete={() => showDeleteConfirmation(job)}
                  />
                  {deleteLoading === job.id && (
                    <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                        <span className="text-sm text-gray-600">Deleting...</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
         )}
       </div>

             {/* Jobs Table */}
       <div>
         <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
           <h2 className="text-lg font-semibold text-gray-900">
             {hasActiveFilters ? 'Filtered' : 'All'} Jobs Table ({filteredTableData.length})
           </h2>
                       <div className="mt-2 sm:mt-0 flex gap-2">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Icon name="search" size="sm" className="text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search in table..."
                  value={tableSearchQuery}
                  onChange={(e) => setTableSearchQuery(e.target.value)}
                  className="block w-full pl-9 pr-4 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white"
                />
                {tableSearchQuery && (
                  <button
                    onClick={() => setTableSearchQuery('')}
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                  >
                    <Icon name="x" size="sm" />
                  </button>
                )}
              </div>
              <select
                value={tableStatusFilter}
                onChange={(e) => setTableStatusFilter(e.target.value)}
                className="block px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white"
              >
                <option value="">All Job Status</option>
                <option value="active">Active</option>
                <option value="draft">Draft</option>
              </select>
            </div>
         </div>
        <Card padding="none">
          <DataTable
            columns={tableColumns}
            data={tableData}
            onSort={(key, direction) => console.log('Sort:', key, direction)}
            onRowClick={(row) => handleViewJob(row as Job)}
            emptyMessage="No jobs found matching your criteria"
          />
        </Card>
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteDialog.isOpen}
        onClose={closeDeleteConfirmation}
        onConfirm={handleDeleteJob}
        title="Delete Job Posting"
        message={`Are you sure you want to delete "${deleteDialog.jobTitle}"? This action cannot be undone and will remove all associated data.`}
        confirmText="Delete Job"
        cancelText="Cancel"
        variant="danger"
        loading={deleteLoading === deleteDialog.jobId}
      />
    </div>
  );
}

export default function JobsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <JobsPageContent />
    </Suspense>
  );
}
