import axios from "axios";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    // Get resume Percentage
    const Job_id = searchParams.get("Job_id");
    const status = searchParams.get("status");

    let records: { Items?: any[] } = {};

    if(Job_id && status){
        const response = await axios.get(
        `${process.env.NODE_RED_URL}/candidate-list`,
        {
          params: { id: Job_id, status },
        },
      );
      records = response.data as { Items?: any[] } || {};
      console.log(records, "<<<<<<< records >>>>>>");
        return new NextResponse(JSON.stringify(records?.Items || []), {
        headers: { "Content-Type": "application/json" },
      });

    } 

    if (id) {
      // Fetch a specific record from the external API
      const { data: record } = await axios.get(
        `${process.env.NODE_RED_URL}/extracted-details`,
        {
          params: { id },
        },
      );

      if (!record) {
        return new NextResponse(JSON.stringify({ error: "Record not found" }), {
          headers: { "Content-Type": "application/json" },
          status: 404,
        });
      }

      return new NextResponse(JSON.stringify(record), {
        headers: { "Content-Type": "application/json" },
      });
    }
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { status?: number; data?: any }; message?: string };
      return NextResponse.json(
        {
          message: "Failed to fetch data from external API",
          error: axiosError.message || "Unknown error",
        },
        { status: axiosError.response?.status || 500 },
      );
    }
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
