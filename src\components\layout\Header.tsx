import React from 'react';
import { cn } from '@/lib/utils';
import { Button, Icon, Input } from '@/components/ui';

interface ProfileData {
  name: string;
  email: string;
  designation: string[];
  role: string[];
  profile_pic_id?: string;
  empID: string;
  employeeno: string;
  organization: string;
}

interface HeaderProps {
  title?: string;
  showSearch?: boolean;
  onSearch?: (query: string) => void;
  actions?: React.ReactNode;
  className?: string;
  onMenuClick?: () => void;
  showMenuButton?: boolean;
}

const Header: React.FC<HeaderProps> = ({
  title,
  showSearch = true,
  onSearch,
  actions,
  className,
  onMenuClick,
  showMenuButton = false,
}) => {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [profile, setProfile] = React.useState<ProfileData | null>(null);
  const [showUserMenu, setShowUserMenu] = React.useState(false);

  // Fetch profile data
  React.useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await fetch('/api/profile');
        if (response.ok) {
          const data = await response.json();
          setProfile(data);
        }
      } catch (error) {
        console.error('Failed to fetch profile:', error);
      }
    };

    fetchProfile();
  }, []);

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <header className={cn('bg-white border-b border-gray-200 px-4 sm:px-6 py-4', className)}>
      <div className="flex items-center justify-between">
        {/* Left side - Menu Button, Title and Search */}
        <div className="flex items-center space-x-3 sm:space-x-6">
          {/* Mobile Menu Button */}
          {showMenuButton && (
            <button
              onClick={onMenuClick}
              className="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label="Open sidebar"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          )}

          {/* {title && (
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900 truncate">{title}</h1>
          )} */}
        </div>

        {/* Right side - Actions and User Menu */}
        <div className="flex items-center space-x-4">
          {actions}
          
          {/* Notifications */}
          <button className="relative p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
            <Icon name="bell" size="md" />
            <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>

          {/* User Menu */}
          <div className="relative">
            <button 
              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100"
              onClick={() => setShowUserMenu(!showUserMenu)}
            >
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {profile ? getInitials(profile.name) : 'U'}
                </span>
              </div>
              <div className="hidden sm:block text-left">
                <p className="text-sm font-medium text-gray-900">
                  {profile?.name || 'Loading...'}
                </p>
                <p className="text-xs text-gray-500">
                  {profile?.designation?.[0] || ''}
                </p>
              </div>
              <Icon name="chevronDown" size="sm" className="text-gray-400" />
            </button>

            {/* Dropdown Menu */}
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border">
                <a
                  href="/profile"
                  className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  onClick={() => setShowUserMenu(false)}
                >
                  View Profile
                </a>
                <a
                  href="/api/auth/logout"
                  className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  Sign out
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
