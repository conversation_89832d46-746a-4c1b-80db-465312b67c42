'use client';

import React from 'react';
import { 
  Card, 
  Button,
  FormField,
  Icon
} from '@/components';

interface ProfileData {
  name: string;
  email: string;
  phone: string;
  designation: string[];
  role: string[];
  organization: string;
  empID: string;
  employeeno: string;
  joiningDate: string;
  joblevel: string;
  reporting_manager: string;
  github?: string;
  linkedin?: string;
  address?: string;
  profile_pic_id?: string;
  status: string;
}

export default function ProfilePage() {
  const [isEditing, setIsEditing] = React.useState(false);
  const [loading, setLoading] = React.useState(true);
  const [profile, setProfile] = React.useState<ProfileData | null>(null);
  const [formData, setFormData] = React.useState<ProfileData | null>(null);

  // Fetch profile data
  React.useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/profile');
        if (response.ok) {
          const data = await response.json();
          setProfile(data);
          setFormData(data);
        } else {
          console.error('Failed to fetch profile');
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  const handleFieldChange = (name: string, value: string) => {
    if (formData) {
      setFormData(prev => ({
        ...prev!,
        [name]: value
      }));
    }
  };

  const handleSave = () => {
    console.log('Save profile:', formData);
    setProfile(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(profile);
    setIsEditing(false);
  };

  const handleUploadAvatar = () => {
    console.log('Upload avatar');
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!profile || !formData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Failed to load profile data</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Profile</h1>
          <p className="text-gray-600">Manage your account settings and preferences</p>
        </div>
        {/* {!isEditing ? (
          <Button variant="primary" onClick={() => setIsEditing(true)}>
            Edit Profile
          </Button>
        ) : (
          <div className="flex space-x-3">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button variant="primary" onClick={handleSave}>
              Save Changes
            </Button>
          </div>
        )} */}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Picture */}
        <div className="lg:col-span-1">
          <Card title="Profile Picture" padding="lg">
            <div className="text-center">
              <div className="w-32 h-32 bg-blue-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-white text-3xl font-medium">
                  {getInitials(profile.name)}
                </span>
              </div>
              <Button variant="outline" onClick={handleUploadAvatar}>
                <Icon name="upload" size="sm" className="mr-2" />
                Upload Photo
              </Button>
              <p className="text-sm text-gray-500 mt-2">
                JPG, PNG or GIF. Max size 2MB.
              </p>
            </div>
          </Card>

          {/* Employee Info */}
          <Card title="Employee Information" padding="lg" className="mt-6">
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Employee ID</span>
                <span className="font-semibold text-gray-500">{profile.employeeno}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Organization</span>
                <span className="font-semibold capitalize text-gray-500">{profile.organization}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Joining Date</span>
                <span className="font-semibold text-gray-500">{formatDate(profile.joiningDate)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Job Level</span>
                <span className="font-semibold capitalize text-gray-500">{profile.joblevel.replace('-', ' ')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Status</span>
                <span className={`font-semibold capitalize ${profile.status === 'active' ? 'text-green-600' : 'text-red-600'}`}>
                  {profile.status}
                </span>
              </div>
            </div>
          </Card>
        </div>

        {/* Profile Information */}
        <div className="lg:col-span-2">
          <Card title="Personal Information" padding="lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-700">
              <FormField
                label="Full Name"
                name="name"
                value={formData.name}
                onChange={handleFieldChange}
                disabled={!isEditing}
                required
              />
              <FormField
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleFieldChange}
                disabled={!isEditing}
                required
              />
              <FormField
                label="Phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleFieldChange}
                disabled={!isEditing}
              />
              <FormField
                label="Reporting Manager"
                name="reporting_manager"
                value={formData.reporting_manager}
                onChange={handleFieldChange}
                disabled={!isEditing}
              />
              <FormField
                label="GitHub Profile"
                name="github"
                value={formData.github || ''}
                onChange={handleFieldChange}
                disabled={!isEditing}
                placeholder="https://github.com/username"
              />
              <FormField
                label="LinkedIn Profile"
                name="linkedin"
                value={formData.linkedin || ''}
                onChange={handleFieldChange}
                disabled={!isEditing}
                placeholder="https://linkedin.com/in/username"
              />
            </div>
            
            <div className="mt-6">
              <FormField
                label="Address"
                name="address"
                type="textarea"
                value={formData.address || ''}
                onChange={handleFieldChange}
                disabled={!isEditing}
                rows={3}
                placeholder="Enter your address"
              />
            </div>
          </Card>

          {/* Roles & Designations */}
          <Card title="Roles & Designations" padding="lg" className="mt-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Current Designations
                </label>
                <div className="flex flex-wrap gap-2">
                  {profile.designation.map((designation, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                    >
                      {designation}
                    </span>
                  ))}
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Assigned Roles
                </label>
                <div className="flex flex-wrap gap-2">
                  {profile.role.map((role, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"
                    >
                      {role}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}

