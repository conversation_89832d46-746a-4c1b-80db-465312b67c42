import { NextRequest, NextResponse } from 'next/server';
import { ConfidentialClientApplication } from '@azure/msal-node';
import axios from 'axios';

const config = {
  auth: {
    clientId: process.env.CLIENT_ID!,
    authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
    clientSecret: process.env.CLIENT_SECRET!,
  },
};

const cca = new ConfidentialClientApplication(config);

export async function POST(req: NextRequest) {
  const body = await req.json();
  const { subject, content, startDateTime, endDateTime, attendeeEmail, attendeeName } = body;

  try {
    const result = await cca.acquireTokenByClientCredential({
      scopes: ['https://graph.microsoft.com/.default'],
    });

    if (!result || !result.accessToken) {
      throw new Error('Failed to acquire access token');
    }
    const accessToken = result.accessToken;

    const event = {
      subject,
      body: {
        contentType: 'HTML',
        content,
      },
      start: {
        dateTime: startDateTime,
        timeZone: 'India Standard Time',
      },
      end: {
        dateTime: endDateTime,
        timeZone: 'India Standard Time',
      },
      location: {
        displayName: 'Online',
      },
      attendees: [
        {
          emailAddress: {
            address: attendeeEmail,
            name: attendeeName,
          },
          type: 'Required',
        },
      ],
    };

    interface GraphEventResponse {
      id: string;
      [key: string]: any;
    }

    const response = await axios.post<GraphEventResponse>(
      `https://graph.microsoft.com/v1.0/users/${process.env.SENDER_EMAIL}/events`,
      event,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return NextResponse.json({
      message: 'Interview scheduled successfully',
      eventId: response.data.id,
    });
  } catch (error: any) {
    console.error('Error scheduling interview:', error.response?.data || error.message);
    return NextResponse.json({ error: 'Failed to schedule interview' }, { status: 500 });
  }
}
