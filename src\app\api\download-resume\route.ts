import axios from "axios";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const requestHeaders = new Headers(request.headers);
  const user_id = requestHeaders.get("X-Auth-User-ID")!; // Retrieve user ID from headers
  const { searchParams } = new URL(request.url);
  const id = searchParams.get("id");
  console.log(id, "Received ID");

  // Check if ID is provided in the request
  if (!id) {
    return NextResponse.json(
      { error: "ID parameter is required" },
      { status: 400 },
    );
  }
  try {
    // First GET request to get the image URL
    const urlResponse = await axios.get(
      `${process.env.NODE_RED_URL}/download_url?fileuuid=${id}&application=hiring`,
      {
        headers: {
          "Content-Type": "application/json",
          context_user: user_id, // Pass context_user in the first request
        },
      },
    );
    const imageUrl = (urlResponse.data as { url: string }).url;
    console.log(imageUrl, "Image URL obtained");

    // Check if the image URL is found
    if (!imageUrl) {
      return NextResponse.json(
        { error: "Image URL not found" },
        { status: 404 },
      );
    }

    // Extract the file name from the URL
    const urlParts = imageUrl.split("/");
    const fullFileName = urlParts[urlParts.length - 1];
    console.log(fullFileName, "Full file name obtained");

    // Extracting the original file name assuming it's after the first hyphen
    const originalFileNameParts = fullFileName.split("-");
    const originalFileName =
      originalFileNameParts.length > 1
        ? originalFileNameParts.slice(1).join("-")
        : fullFileName;

    console.log(originalFileName, "Extracted original file name");

    // Second GET request to fetch the image
    const imageResponse = await axios({
      url: `${process.env.NODE_RED_URL}${imageUrl}`,
      method: "GET",
      responseType: "arraybuffer",
      headers: {
        context_user: user_id,
      },
    });

    console.log(imageResponse, "Image fetched successfully");
    const buffer = Buffer.from(imageResponse.data as ArrayBuffer);
    const res = new NextResponse(buffer, {
      status: 200,
      headers: {
        "Content-Type": "application/pdf", // Set content type for the image
        "Content-Disposition": `attachment; filename="${originalFileName}"`, // Use the extracted original file name dynamically
        "Content-Length": buffer.byteLength.toString(), // Set the content length as a string
      },
    });

    console.log(res, "Response with image and original file name");

    return res; // Send the response with the image data
  } catch (error) {
    console.error("Error fetching the image:", error);
    return NextResponse.json(
      { error: "Failed to download image" },
      { status: 500 },
    );
  }
}
