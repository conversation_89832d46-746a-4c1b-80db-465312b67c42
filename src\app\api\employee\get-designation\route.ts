import axios from "axios";
import { NextRequest, NextResponse } from "next/server";

export async function GET(_request: NextRequest) {
  try {
    let record;
 const response = await axios.get(
        `${process.env.NODE_RED_URL}/Get-Designation-Employee`);
      record = response.data;
      console.log("record-----", record);
        return new NextResponse(
        JSON.stringify(record),
        {
          headers: { "Content-Type": "application/json" },
          status: 200,
        },
      );

  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { status?: number; data?: any }; message?: string };
      return new NextResponse(
        JSON.stringify({
          message: "Failed to fetch data from external API",
          error: axiosError.message || "Unknown error",
        }),
        { status: axiosError.response?.status || 500 },
      );
    }

    return new NextResponse(
      JSON.stringify({ error: "Internal server error" }),
      {
        status: 500,
      },
    );
  }
}