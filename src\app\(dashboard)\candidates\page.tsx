'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  <PERSON>ton,
  Card,
  DataTable,
  Badge,
  CandidateCard,
  SearchBar
} from '@/components';
import { Candidate } from '@/types';
import { cn, formatRelativeTime } from '@/lib/utils';

export default function CandidatesPage() {
  const router = useRouter();

   const [isLoading, setIsLoading] = React.useState(false);
  const [recentCandidateData, setRecentCandidateData] = React.useState<any>([]);
  const [allCandidateData, setAllCandidateData] = React.useState<any>([]);
  const [searchQuery, setSearchQuery] = React.useState('');


     const fetchCandidate = async () => {
  try {
    setIsLoading(true);
    const response = await fetch('/api/candidate');

    if (!response.ok) {
      throw new Error('Failed to fetch candidate');
    }

    const data = await response.json();

    console.log(data?.Items, "<<<<<<< Candidate Data >>>>>>");
    setAllCandidateData(data?.Items);

         // Sort and get latest 3 by created_on
     const sortedData = data?.Items?.filter((item: any) => item.created_on).sort((a: any, b: any) => new Date(b.created_on).getTime() - new Date(a.created_on).getTime());

     const latestThree = sortedData.slice(0, 3);

     console.log(latestThree, "<<<<<<< Recent 3 Candidates >>>>>>");
     setRecentCandidateData(latestThree);
  } catch (error) {
    console.error('Error fetching candidate:', error);
  } finally {
    setIsLoading(false);
  }
};

useEffect(() => {
  fetchCandidate();
}, []);

console.log(recentCandidateData, "<<<<<<< Recent Candidate Data >>>>>>");


  // Sample candidate data
  const candidates: Candidate[] = [
    {
      id: '1',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'screening',
      appliedJobs: ['1'],
      skills: ['React', 'TypeScript', 'Node.js', 'Python'],
      experience: 5,
      location: 'San Francisco, CA',
      appliedDate: new Date('2024-01-20')
    },
    {
      id: '2',
      name: 'Mike Chen',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'interview',
      appliedJobs: ['1', '3'],
      skills: ['JavaScript', 'React', 'AWS', 'Docker'],
      experience: 7,
      location: 'Seattle, WA',
      appliedDate: new Date('2024-01-18')
    },
    {
      id: '3',
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'offer',
      appliedJobs: ['2'],
      skills: ['Figma', 'Adobe XD', 'User Research', 'Prototyping'],
      experience: 4,
      location: 'Austin, TX',
      appliedDate: new Date('2024-01-15')
    },
    {
      id: '4',
      name: 'David Kim',
      email: '<EMAIL>',
      status: 'hired',
      appliedJobs: ['3'],
      skills: ['Python', 'Django', 'PostgreSQL', 'Redis'],
      experience: 6,
      location: 'New York, NY',
      appliedDate: new Date('2024-01-10')
    }
  ];



  const tableColumns = [
    { key: 'name', title: 'Name', sortable: true },
    { key: 'email', title: 'Email', sortable: true },
    { key: 'experience', title: 'Experience', sortable: true },
    { 
      key: 'status', 
      title: 'Status', 
      render: (value: string) => (
        <Badge variant={
          value === 'hired' ? 'success' : 
          value === 'offer' ? 'success' : 
          value === 'interview' ? 'warning' : 
          'default'
        }>
          {value}
        </Badge>
      )
    },
    { key: 'appliedDate', title: 'Applied Date', sortable: true }
  ];

          // Filter candidates based on search query
     const filteredCandidates = allCandidateData?.filter((candidate: any) => {
       if (!searchQuery.trim()) return true;
       
       const query = searchQuery.toLowerCase();
       return (
         candidate?.name?.toLowerCase().includes(query) ||
         candidate?.email?.toLowerCase().includes(query) ||
         candidate?.skills?.some((skill: string) => skill.toLowerCase().includes(query)) ||
         candidate?.status?.toLowerCase().includes(query)
       );
     });

     const tableData = filteredCandidates?.map((candidate: any) => ({
       ...candidate,
       experience: `${candidate?.years_of_experience} years`,
       appliedDate: candidate.created_on
     }));

  const handleCandidateAction = (action: string, candidate: any) => {
    if (action === 'view') {
      router.push(`/candidates/${candidate.candidateid}`);
    } else {
      console.log(`${action} candidate:`, candidate);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200">
      <div className="space-y-6 p-6">
        {/* Header Actions */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm border border-white/20">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-800 bg-clip-text text-transparent">
                Candidates
              </h1>
              <p className="text-gray-600 mt-1">Manage and review candidate applications</p>
            </div>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <Button 
                variant="primary" 
                className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5"
                onClick={() => router.push('/resume-analysis')}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Candidate
              </Button>
            </div>
          </div>
        </div>



         {isLoading ? (
   <div className="space-y-6">
     <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-sm border border-white/20">
       <div className="text-center">
         <div className="animate-spin inline-block w-8 h-8 border-3 border-blue-600 border-t-transparent rounded-full"></div>
         <p className="mt-4 text-gray-600 font-medium">Loading candidates...</p>
       </div>
     </div>
   </div>
 ) : (
   <>
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm border border-white/20">
       <div className="flex items-center justify-between mb-6">
         <div>
           <h2 className="text-2xl font-bold text-gray-900 mb-1">Recent Applications</h2>
           <p className="text-gray-600 text-sm">Latest candidate submissions</p>
         </div>
         <div className="flex items-center space-x-2">
           <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
           <span className="text-sm text-gray-600">Live Updates</span>
         </div>
       </div>
       
       <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
         {recentCandidateData?.map((candidate: any, index: number) => (
           <div
             key={candidate?.candidateid}
             className="group relative bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100 overflow-hidden"
             onClick={() => handleCandidateAction('view', candidate)}
           >
             {/* Gradient overlay */}
             <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
             
             {/* Status indicator */}
             <div className="absolute top-4 right-4">
               <Badge 
                 variant={
                   candidate?.status === 'hired' ? 'success' : 
                   candidate?.status === 'offer' ? 'success' : 
                   candidate?.status === 'interview' ? 'warning' : 
                   'default'
                 }
                 className="text-xs font-medium shadow-sm"
               >
                 {candidate?.status || 'new'}
               </Badge>
             </div>
             
             <div className="p-6 relative z-10">
               {/* Avatar and name section */}
               <div className="flex items-center mb-4">
                 <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                   {(candidate?.name || 'U').charAt(0).toUpperCase()}
                 </div>
                 <div className="flex-1 min-w-0">
                   <h3 className="font-semibold text-gray-900 text-lg truncate">
                     {candidate?.name || 'Unknown'}
                   </h3>
                   <p className="text-sm text-gray-500 truncate">
                     {candidate?.email || 'No email'}
                   </p>
                 </div>
               </div>
               
               {/* Experience and skills */}
               <div className="space-y-3">
                 <div className="flex items-center text-sm text-gray-600">
                   <svg className="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.815-8.864-2.245M21 13.255v4.745A2.25 2.25 0 0118.75 20H5.25A2.25 2.25 0 013 17.955v-4.745M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.815-8.864-2.245" />
                   </svg>
                   <span className="font-medium">{candidate?.years_of_experience || 0} years experience</span>
                 </div>
                 
                 {candidate?.skills && candidate.skills.length > 0 && (
                   <div>
                     <p className="text-xs font-medium text-gray-500 mb-2">SKILLS</p>
                     <div className="flex flex-wrap gap-1.5">
                       {candidate.skills.slice(0, 3).map((skill: string, skillIndex: number) => (
                         <span
                           key={skillIndex}
                           className="inline-block px-2.5 py-1 text-xs font-medium bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 rounded-full border border-blue-200"
                         >
                           {skill}
                         </span>
                       ))}
                       {candidate.skills.length > 3 && (
                         <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                           +{candidate.skills.length - 3} more
                         </span>
                       )}
                     </div>
                   </div>
                 )}
               </div>
               
               {/* Footer with date and action */}
               <div className="mt-4 pt-4 border-t border-gray-100">
                 <div className="flex justify-between items-center">
                   <div className="flex items-center text-xs text-gray-500">
                     <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                     </svg>
                     {candidate?.created_on ? new Date(candidate.created_on).toLocaleDateString() : 'N/A'}
                   </div>
                   <button
                     onClick={(e) => {
                       e.stopPropagation();
                       handleCandidateAction('view', candidate);
                     }}
                     className="text-sm text-blue-600 hover:text-blue-800 font-medium bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded-full transition-colors duration-200"
                   >
                     View Profile
                   </button>
                 </div>
               </div>
             </div>
             
             {/* Hover effect border */}
             <div className="absolute inset-0 border-2 border-transparent group-hover:border-blue-200 rounded-xl transition-colors duration-300"></div>
           </div>
         ))}
       </div>
       
       {/* Empty state */}
       {(!recentCandidateData || recentCandidateData.length === 0) && (
         <div className="text-center py-12">
           <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
             <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
               <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
             </svg>
           </div>
           <h3 className="text-lg font-medium text-gray-900 mb-2">No Recent Applications</h3>
           <p className="text-gray-500 text-sm">New candidate applications will appear here</p>
         </div>
       )}
     </div>

         <div className="mt-8">
           <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm border border-white/20">
             <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
               <h2 className="text-xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-800 bg-clip-text text-transparent">
                 All Candidates
               </h2>
               <div className="flex items-center gap-3">
                 <SearchBar
                   placeholder="Search by name, email or status"
                   value={searchQuery}
                   onChange={setSearchQuery}
                   onSearch={(query) => setSearchQuery(query)}
                   showFilters={false}
                   className="w-80"
                 />
               </div>
             </div>
             <DataTable
               loading={isLoading}
               columns={tableColumns}
               data={tableData || []}
               onSort={(key, direction) => console.log('Sort:', key, direction)}
               onRowClick={(row) => router.push(`/candidates/${row.candidateid}`)}
             />
           </div>
         </div>
       </>
     )}
     </div>
   </div>
 );
}
